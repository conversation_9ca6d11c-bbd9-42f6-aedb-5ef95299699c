<?php
declare(strict_types=1);

namespace CopeX\ProductInheritance\Api;

use Magento\Catalog\Api\Data\ProductInterface;

interface InheritanceProcessorInterface
{
    /**
     * Process inheritance for a specific type of data
     *
     * @param ProductInterface $targetProduct
     * @param ProductInterface $sourceProduct
     * @return bool
     */
    public function process(ProductInterface $targetProduct, ProductInterface $sourceProduct): bool;

    /**
     * Get processor name for logging
     *
     * @return string
     */
    public function getName(): string;

    /**
     * Check if processor is enabled
     *
     * @return bool
     */
    public function isEnabled(): bool;
}
