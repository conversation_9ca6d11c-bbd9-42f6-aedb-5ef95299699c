<?php
declare(strict_types=1);

namespace CopeX\ProductInheritance\Console\Command;

use CopeX\ProductInheritance\Service\ProductInheritanceService;
use Magento\Catalog\Model\ResourceModel\Product\Collection as ProductCollection;
use Magento\Catalog\Model\ResourceModel\Product\CollectionFactory as ProductCollectionFactory;
use Magento\Framework\App\State;
use Magento\Framework\Console\Cli;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Helper\ProgressBar;

class SyncProductsCommand extends Command
{
    const OPTION_PRODUCT_IDS = 'product-ids';
    const OPTION_BATCH_SIZE = 'batch-size';
    const OPTION_DRY_RUN = 'dry-run';

    private ProductInheritanceService $inheritanceService;
    private ProductCollectionFactory $productCollectionFactory;
    private State $appState;

    public function __construct(
        ProductInheritanceService $inheritanceService,
        ProductCollectionFactory $productCollectionFactory,
        State $appState,
        string $name = null
    ) {
        $this->inheritanceService = $inheritanceService;
        $this->productCollectionFactory = $productCollectionFactory;
        $this->appState = $appState;
        parent::__construct($name);
    }

    /**
     * Configure command
     */
    protected function configure(): void
    {
        $this->setName('copex:product-inheritance:sync')
            ->setDescription('Sync product attributes based on inherit_from_sku field')
            ->addOption(
                self::OPTION_PRODUCT_IDS,
                'p',
                InputOption::VALUE_OPTIONAL,
                'Comma-separated list of product IDs to sync (if not provided, all products with inherit_from_sku will be processed)'
            )
            ->addOption(
                self::OPTION_BATCH_SIZE,
                'b',
                InputOption::VALUE_OPTIONAL,
                'Batch size for processing',
                100
            )
            ->addOption(
                self::OPTION_DRY_RUN,
                'd',
                InputOption::VALUE_NONE,
                'Dry run - show what would be processed without making changes'
            );
    }

    /**
     * Execute command
     *
     * @param InputInterface $input
     * @param OutputInterface $output
     * @return int
     */
    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        try {
            $this->appState->setAreaCode(\Magento\Framework\App\Area::AREA_ADMINHTML);
        } catch (\Exception $e) {
            // Area already set
        }

        $productIds = $input->getOption(self::OPTION_PRODUCT_IDS);
        $batchSize = (int) $input->getOption(self::OPTION_BATCH_SIZE);
        $isDryRun = $input->getOption(self::OPTION_DRY_RUN);

        $output->writeln('<info>Starting Product Inheritance Sync...</info>');
        
        if ($isDryRun) {
            $output->writeln('<comment>DRY RUN MODE - No changes will be made</comment>');
        }

        // Get products to process
        $products = $this->getProductsToProcess($productIds);
        
        if (empty($products)) {
            $output->writeln('<comment>No products found with inherit_from_sku field set.</comment>');
            return Cli::RETURN_SUCCESS;
        }

        $totalProducts = count($products);
        $output->writeln("<info>Found {$totalProducts} products to process</info>");

        // Create progress bar
        $progressBar = new ProgressBar($output, $totalProducts);
        $progressBar->setFormat('verbose');
        $progressBar->start();

        $successCount = 0;
        $errorCount = 0;
        $skippedCount = 0;

        // Process in batches
        $batches = array_chunk($products, $batchSize);
        
        foreach ($batches as $batch) {
            if ($isDryRun) {
                foreach ($batch as $product) {
                    $inheritFromSku = $product->getData('inherit_from_sku');
                    $output->writeln("\n<comment>Would process Product ID {$product->getId()} (SKU: {$product->getSku()}) inheriting from SKU: {$inheritFromSku}</comment>");
                    $progressBar->advance();
                }
                continue;
            }

            $batchResults = $this->inheritanceService->processMultipleProducts(
                array_map(function($product) { return $product->getId(); }, $batch)
            );

            foreach ($batchResults as $productId => $result) {
                if ($result === true) {
                    $successCount++;
                } elseif ($result === false) {
                    $errorCount++;
                } else {
                    $skippedCount++;
                }
                $progressBar->advance();
            }
        }

        $progressBar->finish();
        $output->writeln('');

        // Output results
        if ($isDryRun) {
            $output->writeln("<info>Dry run completed. {$totalProducts} products would be processed.</info>");
        } else {
            $output->writeln('<info>Sync completed!</info>');
            $output->writeln("<info>Successful: {$successCount}</info>");
            $output->writeln("<info>Errors: {$errorCount}</info>");
            $output->writeln("<info>Skipped: {$skippedCount}</info>");
        }

        return Cli::RETURN_SUCCESS;
    }

    /**
     * Get products to process
     *
     * @param string|null $productIds
     * @return array
     */
    private function getProductsToProcess(?string $productIds): array
    {
        /** @var ProductCollection $collection */
        $collection = $this->productCollectionFactory->create();
        
        if ($productIds) {
            $ids = array_map('trim', explode(',', $productIds));
            $collection->addFieldToFilter('entity_id', ['in' => $ids]);
        }
        
        // Only get products that have inherit_from_sku set
        $collection->addFieldToFilter('inherit_from_sku', ['notnull' => true]);
        $collection->addFieldToFilter('inherit_from_sku', ['neq' => '']);
        
        $collection->addAttributeToSelect(['sku', 'inherit_from_sku']);
        
        return $collection->getItems();
    }
}
