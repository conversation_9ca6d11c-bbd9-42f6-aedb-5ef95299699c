<?php
declare(strict_types=1);

namespace CopeX\ProductInheritance\Helper;

use Magento\Framework\App\Helper\AbstractHelper;
use Magento\Framework\App\Helper\Context;
use Magento\Store\Model\ScopeInterface;

class Data extends AbstractHelper
{
    const XML_PATH_ENABLED = 'copex_product_inheritance/general/enabled';
    const XML_PATH_AUTO_SYNC = 'copex_product_inheritance/general/auto_sync';
    const XML_PATH_LOG_ENABLED = 'copex_product_inheritance/general/log_enabled';
    const XML_PATH_INHERIT_ATTRIBUTES = 'copex_product_inheritance/attributes/inherit_attributes';
    const XML_PATH_EXCLUDE_EMPTY = 'copex_product_inheritance/attributes/exclude_empty';
    const XML_PATH_OVERWRITE_EXISTING = 'copex_product_inheritance/attributes/overwrite_existing';
    const XML_PATH_ENABLE_IMAGE_PROCESSOR = 'copex_product_inheritance/processors/enable_image_processor';
    const XML_PATH_ENABLE_ATTACHMENT_PROCESSOR = 'copex_product_inheritance/processors/enable_attachment_processor';

    /**
     * Check if product inheritance is enabled
     *
     * @param int|null $storeId
     * @return bool
     */
    public function isEnabled(?int $storeId = null): bool
    {
        return $this->scopeConfig->isSetFlag(
            self::XML_PATH_ENABLED,
            ScopeInterface::SCOPE_STORE,
            $storeId
        );
    }

    /**
     * Check if auto sync on product save is enabled
     *
     * @param int|null $storeId
     * @return bool
     */
    public function isAutoSyncEnabled(?int $storeId = null): bool
    {
        return $this->scopeConfig->isSetFlag(
            self::XML_PATH_AUTO_SYNC,
            ScopeInterface::SCOPE_STORE,
            $storeId
        );
    }

    /**
     * Check if logging is enabled
     *
     * @param int|null $storeId
     * @return bool
     */
    public function isLoggingEnabled(?int $storeId = null): bool
    {
        return $this->scopeConfig->isSetFlag(
            self::XML_PATH_LOG_ENABLED,
            ScopeInterface::SCOPE_STORE,
            $storeId
        );
    }

    /**
     * Get list of attributes to inherit
     *
     * @param int|null $storeId
     * @return array
     */
    public function getInheritAttributes(?int $storeId = null): array
    {
        $value = $this->scopeConfig->getValue(
            self::XML_PATH_INHERIT_ATTRIBUTES,
            ScopeInterface::SCOPE_STORE,
            $storeId
        );
        
        return $value ? explode(',', $value) : [];
    }

    /**
     * Check if empty values should be excluded
     *
     * @param int|null $storeId
     * @return bool
     */
    public function shouldExcludeEmpty(?int $storeId = null): bool
    {
        return $this->scopeConfig->isSetFlag(
            self::XML_PATH_EXCLUDE_EMPTY,
            ScopeInterface::SCOPE_STORE,
            $storeId
        );
    }

    /**
     * Check if existing values should be overwritten
     *
     * @param int|null $storeId
     * @return bool
     */
    public function shouldOverwriteExisting(?int $storeId = null): bool
    {
        return $this->scopeConfig->isSetFlag(
            self::XML_PATH_OVERWRITE_EXISTING,
            ScopeInterface::SCOPE_STORE,
            $storeId
        );
    }

    /**
     * Check if image processor is enabled
     *
     * @param int|null $storeId
     * @return bool
     */
    public function isImageProcessorEnabled(?int $storeId = null): bool
    {
        return $this->scopeConfig->isSetFlag(
            self::XML_PATH_ENABLE_IMAGE_PROCESSOR,
            ScopeInterface::SCOPE_STORE,
            $storeId
        );
    }

    /**
     * Check if attachment processor is enabled
     *
     * @param int|null $storeId
     * @return bool
     */
    public function isAttachmentProcessorEnabled(?int $storeId = null): bool
    {
        return $this->scopeConfig->isSetFlag(
            self::XML_PATH_ENABLE_ATTACHMENT_PROCESSOR,
            ScopeInterface::SCOPE_STORE,
            $storeId
        );
    }
}
