<?php
declare(strict_types=1);

namespace CopeX\ProductInheritance\Model;

use Magento\Framework\App\Config\ScopeConfigInterface;
use Magento\Store\Model\ScopeInterface;

class Config
{
    private const XML_PATH_ENABLED = 'copex_product_inheritance/general/enabled';
    private const XML_PATH_AUTO_SYNC = 'copex_product_inheritance/general/auto_sync';
    private const XML_PATH_LOG_ENABLED = 'copex_product_inheritance/general/log_enabled';
    private const XML_PATH_INHERIT_ATTRIBUTES = 'copex_product_inheritance/attributes/inherit_attributes';
    private const XML_PATH_EXCLUDE_EMPTY = 'copex_product_inheritance/attributes/exclude_empty';
    private const XML_PATH_OVERWRITE_EXISTING = 'copex_product_inheritance/attributes/overwrite_existing';
    private const XML_PATH_ENABLE_IMAGE_PROCESSOR = 'copex_product_inheritance/processors/enable_image_processor';
    private const XML_PATH_ENABLE_ATTACHMENT_PROCESSOR = 'copex_product_inheritance/processors/enable_attachment_processor';
    private const XML_PATH_ENABLE_LINKED_PRODUCTS_PROCESSOR = 'copex_product_inheritance/processors/enable_linked_products_processor';

    public function __construct(
        private readonly ScopeConfigInterface $scopeConfig
    ) {
    }

    public function isEnabled(?int $storeId = null): bool
    {
        return $this->scopeConfig->isSetFlag(
            self::XML_PATH_ENABLED,
            ScopeInterface::SCOPE_STORE,
            $storeId
        );
    }

    public function isAutoSyncEnabled(?int $storeId = null): bool
    {
        return $this->scopeConfig->isSetFlag(
            self::XML_PATH_AUTO_SYNC,
            ScopeInterface::SCOPE_STORE,
            $storeId
        );
    }

    public function isLoggingEnabled(?int $storeId = null): bool
    {
        return $this->scopeConfig->isSetFlag(
            self::XML_PATH_LOG_ENABLED,
            ScopeInterface::SCOPE_STORE,
            $storeId
        );
    }

    public function getInheritAttributes(?int $storeId = null): array
    {
        $value = $this->scopeConfig->getValue(
            self::XML_PATH_INHERIT_ATTRIBUTES,
            ScopeInterface::SCOPE_STORE,
            $storeId
        );
        
        return $value ? explode(',', $value) : [];
    }

    public function shouldExcludeEmpty(?int $storeId = null): bool
    {
        return $this->scopeConfig->isSetFlag(
            self::XML_PATH_EXCLUDE_EMPTY,
            ScopeInterface::SCOPE_STORE,
            $storeId
        );
    }

    public function shouldOverwriteExisting(?int $storeId = null): bool
    {
        return $this->scopeConfig->isSetFlag(
            self::XML_PATH_OVERWRITE_EXISTING,
            ScopeInterface::SCOPE_STORE,
            $storeId
        );
    }

    public function isImageProcessorEnabled(?int $storeId = null): bool
    {
        return $this->scopeConfig->isSetFlag(
            self::XML_PATH_ENABLE_IMAGE_PROCESSOR,
            ScopeInterface::SCOPE_STORE,
            $storeId
        );
    }

    public function isAttachmentProcessorEnabled(?int $storeId = null): bool
    {
        return $this->scopeConfig->isSetFlag(
            self::XML_PATH_ENABLE_ATTACHMENT_PROCESSOR,
            ScopeInterface::SCOPE_STORE,
            $storeId
        );
    }

    public function isLinkedProductsProcessorEnabled(?int $storeId = null): bool
    {
        return $this->scopeConfig->isSetFlag(
            self::XML_PATH_ENABLE_LINKED_PRODUCTS_PROCESSOR,
            ScopeInterface::SCOPE_STORE,
            $storeId
        );
    }
}
