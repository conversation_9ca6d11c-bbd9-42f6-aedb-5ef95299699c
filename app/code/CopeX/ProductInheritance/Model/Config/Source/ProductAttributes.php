<?php
declare(strict_types=1);

namespace CopeX\ProductInheritance\Model\Config\Source;

use Magento\Catalog\Model\Product;
use Magento\Eav\Model\Config as EavConfig;
use Magento\Framework\Option\ArrayInterface;

class ProductAttributes implements ArrayInterface
{
    // Specific attributes that should be available for inheritance
    private const SPECIFIC_ATTRIBUTES = [
        'subname' => 'Subheadline',
        'detaildescription_intro' => 'Detailbeschreibung Einleitung',
        'short_description' => 'Short Description',
        'description' => 'Description',
        'detailtophint' => 'Detail - Top Hint',
        'detailtopusp1' => 'Detail - Top USP 1',
        'detailtopusp2' => 'Detail - Top USP 2',
        'detailtopusp3' => 'Detail - Top USP 3',
        'detailtopusp4' => 'Detail - Top USP 4',
        'detailtopusp5' => 'Detail - Top USP 5',
        'detailtopusp6' => 'Detail - Top USP 6',
        'listheading' => 'Liste Ueberschrift',
        'listdescription' => 'Beschreibung Liste',
        'listsize' => 'Größe Liste',
        'bottom_listview' => 'Letzte Zeile Liste',
        'meta_title' => 'Meta Title',
        'meta_keyword' => 'Meta Keywords',
        'meta_description' => 'Meta Description',
        'feed_name' => 'Feed Name',
        'feed_description' => 'Feed Description',
        'in_feed' => 'In Feed'
    ];

    public function __construct(
        private readonly EavConfig $eavConfig
    ) {
    }

    /**
     * Return array of options as value-label pairs
     *
     * @return array
     */
    public function toOptionArray(): array
    {
        $options = [];

        // Add specific attributes first
        foreach (self::SPECIFIC_ATTRIBUTES as $code => $label) {
            $options[] = [
                'value' => $code,
                'label' => $label . ' (' . $code . ')'
            ];
        }

        // Add additional user-defined attributes
        $entityType = $this->eavConfig->getEntityType(Product::ENTITY);
        $attributes = $entityType->getAttributeCollection();

        foreach ($attributes as $attribute) {
            $attributeCode = $attribute->getAttributeCode();

            // Skip if already in specific attributes or excluded
            if (isset(self::SPECIFIC_ATTRIBUTES[$attributeCode]) || $this->isExcludedAttribute($attributeCode)) {
                continue;
            }

            // Only include user-defined attributes
            if ($attribute->getIsUserDefined()) {
                $options[] = [
                    'value' => $attributeCode,
                    'label' => ($attribute->getFrontendLabel() ?: $attributeCode) . ' (' . $attributeCode . ')'
                ];
            }
        }

        // Sort by label
        usort($options, function ($a, $b) {
            return strcmp($a['label'], $b['label']);
        });

        return $options;
    }

    /**
     * Check if attribute should be excluded from inheritance
     *
     * @param string $attributeCode
     * @return bool
     */
    private function isExcludedAttribute(string $attributeCode): bool
    {
        $excludedAttributes = [
            'entity_id',
            'entity_type_id',
            'attribute_set_id',
            'type_id',
            'sku',
            'has_options',
            'required_options',
            'created_at',
            'updated_at',
            'inherit_from_sku', // Don't inherit the inheritance field itself
            'url_key',
            'url_path',
            'status',
            'visibility',
            'price',
            'special_price',
            'special_from_date',
            'special_to_date',
            'weight'
        ];

        return in_array($attributeCode, $excludedAttributes);
    }
}
