<?php
declare(strict_types=1);

namespace CopeX\ProductInheritance\Model\Config\Source;

use Magento\Catalog\Model\Product;
use Magento\Eav\Model\Config as EavConfig;
use Magento\Framework\Option\ArrayInterface;

class ProductAttributes implements ArrayInterface
{
    private EavConfig $eavConfig;

    public function __construct(EavConfig $eavConfig)
    {
        $this->eavConfig = $eavConfig;
    }

    /**
     * Return array of options as value-label pairs
     *
     * @return array
     */
    public function toOptionArray(): array
    {
        $options = [];
        $entityType = $this->eavConfig->getEntityType(Product::ENTITY);
        $attributes = $entityType->getAttributeCollection();
        
        // Exclude system attributes that shouldn't be inherited
        $excludedAttributes = [
            'entity_id',
            'entity_type_id',
            'attribute_set_id',
            'type_id',
            'sku',
            'has_options',
            'required_options',
            'created_at',
            'updated_at',
            'inherit_from_sku', // Don't inherit the inheritance field itself
            'url_key',
            'url_path'
        ];
        
        foreach ($attributes as $attribute) {
            if (in_array($attribute->getAttributeCode(), $excludedAttributes)) {
                continue;
            }
            
            // Only include user-defined attributes and some important system attributes
            if ($attribute->getIsUserDefined() || in_array($attribute->getAttributeCode(), [
                'name',
                'description',
                'short_description',
                'meta_title',
                'meta_keyword',
                'meta_description',
                'subname', // Custom attribute from existing system
                'weight',
                'status',
                'visibility',
                'price',
                'special_price',
                'special_from_date',
                'special_to_date'
            ])) {
                $options[] = [
                    'value' => $attribute->getAttributeCode(),
                    'label' => $attribute->getFrontendLabel() . ' (' . $attribute->getAttributeCode() . ')'
                ];
            }
        }
        
        // Sort by label
        usort($options, function ($a, $b) {
            return strcmp($a['label'], $b['label']);
        });
        
        return $options;
    }
}
