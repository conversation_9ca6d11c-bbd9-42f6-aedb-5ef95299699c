<?php
declare(strict_types=1);

namespace CopeX\ProductInheritance\Model\Processor;

use CopeX\ProductInheritance\Api\InheritanceProcessorInterface;
use CopeX\ProductInheritance\Model\Config;
use Magento\Catalog\Api\Data\ProductInterface;
use Magento\Framework\Module\Manager as ModuleManager;
use Psr\Log\LoggerInterface;

class AttachmentProcessor implements InheritanceProcessorInterface
{
    private const AMASTY_ATTACHMENT_MODULE = 'Amasty_ProductAttachment';
    private const ATTACHMENT_ATTRIBUTES = [
        'attachment_order',
        'attachment_shipping'
    ];

    public function __construct(
        private readonly Config $config,
        private readonly ModuleManager $moduleManager,
        private readonly LoggerInterface $logger
    ) {
    }

    /**
     * Process attachment inheritance
     *
     * @param ProductInterface $targetProduct
     * @param ProductInterface $sourceProduct
     * @return bool
     */
    public function process(ProductInterface $targetProduct, ProductInterface $sourceProduct): bool
    {
        if (!$this->isEnabled()) {
            return false;
        }

        if (!$this->moduleManager->isEnabled(self::AMASTY_ATTACHMENT_MODULE)) {
            $this->logger->info('Amasty Attachment module is not enabled, skipping attachment inheritance');
            return false;
        }

        try {
            $inheritedCount = 0;
            $excludeEmpty = $this->config->shouldExcludeEmpty();
            $overwriteExisting = $this->config->shouldOverwriteExisting();

            foreach (self::ATTACHMENT_ATTRIBUTES as $attributeCode) {
                if ($this->inheritAttachmentAttribute($targetProduct, $sourceProduct, $attributeCode, $excludeEmpty, $overwriteExisting)) {
                    $inheritedCount++;
                }
            }

            if ($inheritedCount > 0) {
                $this->logger->info(
                    sprintf('Inherited %d attachment attributes from %s to %s',
                        $inheritedCount,
                        $sourceProduct->getSku(),
                        $targetProduct->getSku()
                    )
                );
            }

            return $inheritedCount > 0;
        } catch (\Exception $e) {
            $this->logger->error(
                'Error processing attachment inheritance: ' . $e->getMessage(),
                ['target_product_id' => $targetProduct->getId(), 'source_product_id' => $sourceProduct->getId()]
            );
            return false;
        }
    }

    /**
     * Inherit single attachment attribute
     *
     * @param ProductInterface $targetProduct
     * @param ProductInterface $sourceProduct
     * @param string $attributeCode
     * @param bool $excludeEmpty
     * @param bool $overwriteExisting
     * @return bool
     */
    private function inheritAttachmentAttribute(
        ProductInterface $targetProduct,
        ProductInterface $sourceProduct,
        string $attributeCode,
        bool $excludeEmpty,
        bool $overwriteExisting
    ): bool {
        $sourceValue = $sourceProduct->getData($attributeCode);
        $targetValue = $targetProduct->getData($attributeCode);

        // Skip if source value is empty and we should exclude empty values
        if ($excludeEmpty && empty($sourceValue)) {
            return false;
        }

        // Skip if target has value and we shouldn't overwrite
        if (!$overwriteExisting && !empty($targetValue)) {
            return false;
        }

        $targetProduct->setData($attributeCode, $sourceValue);
        return true;
    }

    /**
     * Get processor name
     *
     * @return string
     */
    public function getName(): string
    {
        return 'Attachment Processor';
    }

    /**
     * Check if processor is enabled
     *
     * @return bool
     */
    public function isEnabled(): bool
    {
        return $this->config->isEnabled() &&
               $this->config->isAttachmentProcessorEnabled() &&
               $this->moduleManager->isEnabled(self::AMASTY_ATTACHMENT_MODULE);
    }
}
