<?php
declare(strict_types=1);

namespace CopeX\ProductInheritance\Model\Processor;

use CopeX\ProductInheritance\Api\InheritanceProcessorInterface;
use CopeX\ProductInheritance\Helper\Data as Helper;
use Magento\Catalog\Api\Data\ProductInterface;
use Magento\Framework\Module\Manager as ModuleManager;

class AttachmentProcessor implements InheritanceProcessorInterface
{
    private Helper $helper;
    private ModuleManager $moduleManager;

    public function __construct(
        Helper $helper,
        ModuleManager $moduleManager
    ) {
        $this->helper = $helper;
        $this->moduleManager = $moduleManager;
    }

    /**
     * Process attachment inheritance
     *
     * @param ProductInterface $targetProduct
     * @param ProductInterface $sourceProduct
     * @return bool
     */
    public function process(ProductInterface $targetProduct, ProductInterface $sourceProduct): bool
    {
        if (!$this->isEnabled()) {
            return false;
        }

        // Check if Amasty Product Attachment module is available
        if (!$this->moduleManager->isEnabled('Amasty_Attachment')) {
            return false;
        }

        try {
            // Get attachment attributes from source product
            $attachmentOrder = $sourceProduct->getData('attachment_order');
            $attachmentShipping = $sourceProduct->getData('attachment_shipping');

            $inheritedCount = 0;

            if ($attachmentOrder) {
                $targetProduct->setData('attachment_order', $attachmentOrder);
                $inheritedCount++;
            }

            if ($attachmentShipping) {
                $targetProduct->setData('attachment_shipping', $attachmentShipping);
                $inheritedCount++;
            }

            return $inheritedCount > 0;
        } catch (\Exception $e) {
            return false;
        }
    }

    /**
     * Get processor name
     *
     * @return string
     */
    public function getName(): string
    {
        return 'Attachment Processor';
    }

    /**
     * Check if processor is enabled
     *
     * @return bool
     */
    public function isEnabled(): bool
    {
        return $this->helper->isEnabled() && 
               $this->helper->isAttachmentProcessorEnabled() &&
               $this->moduleManager->isEnabled('Amasty_Attachment');
    }
}
