<?php
declare(strict_types=1);

namespace CopeX\ProductInheritance\Model\Processor;

use CopeX\ProductInheritance\Api\InheritanceProcessorInterface;
use CopeX\ProductInheritance\Helper\Data as Helper;
use Magento\Catalog\Api\Data\ProductInterface;

class AttributeProcessor implements InheritanceProcessorInterface
{
    private Helper $helper;

    public function __construct(Helper $helper)
    {
        $this->helper = $helper;
    }

    /**
     * Process attribute inheritance
     *
     * @param ProductInterface $targetProduct
     * @param ProductInterface $sourceProduct
     * @return bool
     */
    public function process(ProductInterface $targetProduct, ProductInterface $sourceProduct): bool
    {
        if (!$this->isEnabled()) {
            return false;
        }

        $attributesToInherit = $this->helper->getInheritAttributes();
        if (empty($attributesToInherit)) {
            return false;
        }

        $inheritedCount = 0;
        $excludeEmpty = $this->helper->shouldExcludeEmpty();
        $overwriteExisting = $this->helper->shouldOverwriteExisting();

        foreach ($attributesToInherit as $attributeCode) {
            $sourceValue = $sourceProduct->getData($attributeCode);
            $targetValue = $targetProduct->getData($attributeCode);

            // Skip if source value is empty and we should exclude empty values
            if ($excludeEmpty && $this->isEmpty($sourceValue)) {
                continue;
            }

            // Skip if target has value and we shouldn't overwrite
            if (!$overwriteExisting && !$this->isEmpty($targetValue)) {
                continue;
            }

            // Set the value
            $targetProduct->setData($attributeCode, $sourceValue);
            $inheritedCount++;
        }

        return $inheritedCount > 0;
    }

    /**
     * Get processor name
     *
     * @return string
     */
    public function getName(): string
    {
        return 'Attribute Processor';
    }

    /**
     * Check if processor is enabled
     *
     * @return bool
     */
    public function isEnabled(): bool
    {
        return $this->helper->isEnabled();
    }

    /**
     * Check if value is empty
     *
     * @param mixed $value
     * @return bool
     */
    private function isEmpty($value): bool
    {
        if ($value === null || $value === '') {
            return true;
        }

        if (is_array($value) && empty($value)) {
            return true;
        }

        return false;
    }
}
