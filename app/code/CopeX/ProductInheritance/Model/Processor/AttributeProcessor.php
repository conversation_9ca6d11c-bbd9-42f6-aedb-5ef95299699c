<?php
declare(strict_types=1);

namespace CopeX\ProductInheritance\Model\Processor;

use CopeX\ProductInheritance\Api\InheritanceProcessorInterface;
use CopeX\ProductInheritance\Model\Config;
use Magento\Catalog\Api\Data\ProductInterface;
use Psr\Log\LoggerInterface;

class AttributeProcessor implements InheritanceProcessorInterface
{
    public function __construct(
        private readonly Config $config,
        private readonly LoggerInterface $logger
    ) {
    }

    /**
     * Process attribute inheritance
     *
     * @param ProductInterface $targetProduct
     * @param ProductInterface $sourceProduct
     * @return bool
     */
    public function process(ProductInterface $targetProduct, ProductInterface $sourceProduct): bool
    {
        if (!$this->isEnabled()) {
            return false;
        }

        $attributesToInherit = $this->config->getInheritAttributes();
        if (empty($attributesToInherit)) {
            $this->logger->info('No attributes configured for inheritance');
            return false;
        }

        $inheritedCount = 0;
        $excludeEmpty = $this->config->shouldExcludeEmpty();
        $overwriteExisting = $this->config->shouldOverwriteExisting();

        foreach ($attributesToInherit as $attributeCode) {
            if ($this->inheritAttribute($targetProduct, $sourceProduct, $attributeCode, $excludeEmpty, $overwriteExisting)) {
                $inheritedCount++;
            }
        }

        if ($inheritedCount > 0) {
            $this->logger->info(
                sprintf('Inherited %d attributes from %s to %s',
                    $inheritedCount,
                    $sourceProduct->getSku(),
                    $targetProduct->getSku()
                )
            );
        }

        return $inheritedCount > 0;
    }

    /**
     * Inherit single attribute from source to target product
     *
     * @param ProductInterface $targetProduct
     * @param ProductInterface $sourceProduct
     * @param string $attributeCode
     * @param bool $excludeEmpty
     * @param bool $overwriteExisting
     * @return bool
     */
    private function inheritAttribute(
        ProductInterface $targetProduct,
        ProductInterface $sourceProduct,
        string $attributeCode,
        bool $excludeEmpty,
        bool $overwriteExisting
    ): bool {
        $sourceValue = $sourceProduct->getData($attributeCode);
        $targetValue = $targetProduct->getData($attributeCode);

        // Skip if source value is empty and we should exclude empty values
        if ($excludeEmpty && $this->isEmpty($sourceValue)) {
            return false;
        }

        // Skip if target has value and we shouldn't overwrite
        if (!$overwriteExisting && !$this->isEmpty($targetValue)) {
            return false;
        }

        // Set the value
        $targetProduct->setData($attributeCode, $sourceValue);
        return true;
    }

    /**
     * Get processor name
     *
     * @return string
     */
    public function getName(): string
    {
        return 'Attribute Processor';
    }

    /**
     * Check if processor is enabled
     *
     * @return bool
     */
    public function isEnabled(): bool
    {
        return $this->config->isEnabled();
    }

    /**
     * Check if value is empty
     *
     * @param mixed $value
     * @return bool
     */
    private function isEmpty($value): bool
    {
        if ($value === null || $value === '') {
            return true;
        }

        if (is_array($value) && empty($value)) {
            return true;
        }

        return false;
    }
}
