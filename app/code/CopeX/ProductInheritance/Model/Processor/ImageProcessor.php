<?php
declare(strict_types=1);

namespace CopeX\ProductInheritance\Model\Processor;

use CopeX\ProductInheritance\Api\InheritanceProcessorInterface;
use CopeX\ProductInheritance\Model\Config;
use Magento\Catalog\Api\Data\ProductInterface;
use Magento\Catalog\Model\Product\Gallery\Processor as GalleryProcessor;
use Magento\Catalog\Model\Product;
use Psr\Log\LoggerInterface;

class ImageProcessor implements InheritanceProcessorInterface
{
    public function __construct(
        private readonly Config $config,
        private readonly GalleryProcessor $galleryProcessor,
        private readonly LoggerInterface $logger
    ) {
    }

    /**
     * Process image inheritance - copy image references, not files
     *
     * @param ProductInterface $targetProduct
     * @param ProductInterface $sourceProduct
     * @return bool
     */
    public function process(ProductInterface $targetProduct, ProductInterface $sourceProduct): bool
    {
        if (!$this->isEnabled()) {
            return false;
        }

        try {
            /** @var Product $sourceProduct */
            /** @var Product $targetProduct */

            // Get source product media gallery
            $sourceMediaGallery = $sourceProduct->getMediaGalleryEntries();
            if (!$sourceMediaGallery || empty($sourceMediaGallery)) {
                return false;
            }

            $overwriteExisting = $this->config->shouldOverwriteExisting();

            // Check if target already has images and we shouldn't overwrite
            $targetMediaGallery = $targetProduct->getMediaGalleryEntries();
            if (!$overwriteExisting && !empty($targetMediaGallery)) {
                return false;
            }

            // Clear existing images if overwriting
            if ($overwriteExisting && !empty($targetMediaGallery)) {
                $targetProduct->setMediaGalleryEntries([]);
            }

            // Copy media gallery entries (references only)
            $targetProduct->setMediaGalleryEntries($sourceMediaGallery);

            // Copy image role assignments (base, small, thumbnail)
            $this->copyImageRoles($sourceProduct, $targetProduct);

            $this->logger->info(
                sprintf('Inherited %d image references from %s to %s',
                    count($sourceMediaGallery),
                    $sourceProduct->getSku(),
                    $targetProduct->getSku()
                )
            );

            return true;
        } catch (\Exception $e) {
            $this->logger->error(
                'Error processing image inheritance: ' . $e->getMessage(),
                ['target_product_id' => $targetProduct->getId(), 'source_product_id' => $sourceProduct->getId()]
            );
            return false;
        }
    }

    /**
     * Copy image role assignments from source to target product
     *
     * @param Product $sourceProduct
     * @param Product $targetProduct
     * @return void
     */
    private function copyImageRoles(Product $sourceProduct, Product $targetProduct): void
    {
        try {
            // Copy base image
            if ($sourceProduct->getImage() && $sourceProduct->getImage() !== 'no_selection') {
                $targetProduct->setImage($sourceProduct->getImage());
            }

            // Copy small image
            if ($sourceProduct->getSmallImage() && $sourceProduct->getSmallImage() !== 'no_selection') {
                $targetProduct->setSmallImage($sourceProduct->getSmallImage());
            }

            // Copy thumbnail
            if ($sourceProduct->getThumbnail() && $sourceProduct->getThumbnail() !== 'no_selection') {
                $targetProduct->setThumbnail($sourceProduct->getThumbnail());
            }

            // Copy swatch image if exists
            if ($sourceProduct->getSwatchImage() && $sourceProduct->getSwatchImage() !== 'no_selection') {
                $targetProduct->setSwatchImage($sourceProduct->getSwatchImage());
            }
        } catch (\Exception $e) {
            $this->logger->warning(
                'Failed to copy image roles: ' . $e->getMessage(),
                ['source_sku' => $sourceProduct->getSku(), 'target_sku' => $targetProduct->getSku()]
            );
        }
    }

    /**
     * Get processor name
     *
     * @return string
     */
    public function getName(): string
    {
        return 'Image Processor';
    }

    /**
     * Check if processor is enabled
     *
     * @return bool
     */
    public function isEnabled(): bool
    {
        return $this->config->isEnabled() && $this->config->isImageProcessorEnabled();
    }
}
