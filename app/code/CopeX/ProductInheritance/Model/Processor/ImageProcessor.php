<?php
declare(strict_types=1);

namespace CopeX\ProductInheritance\Model\Processor;

use CopeX\ProductInheritance\Api\InheritanceProcessorInterface;
use CopeX\ProductInheritance\Helper\Data as Helper;
use Magento\Catalog\Api\Data\ProductInterface;
use Magento\Catalog\Model\Product\Gallery\Processor as GalleryProcessor;
use Magento\Framework\App\Filesystem\DirectoryList;
use Magento\Framework\Filesystem;
use Magento\Framework\Filesystem\Directory\WriteInterface;

class ImageProcessor implements InheritanceProcessorInterface
{
    private Helper $helper;
    private GalleryProcessor $galleryProcessor;
    private Filesystem $filesystem;
    private WriteInterface $mediaDirectory;

    public function __construct(
        Helper $helper,
        GalleryProcessor $galleryProcessor,
        Filesystem $filesystem
    ) {
        $this->helper = $helper;
        $this->galleryProcessor = $galleryProcessor;
        $this->filesystem = $filesystem;
        $this->mediaDirectory = $this->filesystem->getDirectoryWrite(DirectoryList::MEDIA);
    }

    /**
     * Process image inheritance
     *
     * @param ProductInterface $targetProduct
     * @param ProductInterface $sourceProduct
     * @return bool
     */
    public function process(ProductInterface $targetProduct, ProductInterface $sourceProduct): bool
    {
        if (!$this->isEnabled()) {
            return false;
        }

        try {
            // Get source product images
            $sourceImages = $sourceProduct->getMediaGalleryImages();
            if (!$sourceImages || $sourceImages->getSize() === 0) {
                return false;
            }

            $copiedCount = 0;

            foreach ($sourceImages as $image) {
                try {
                    $sourceFile = $image->getFile();
                    if (!$sourceFile) {
                        continue;
                    }

                    // Generate new filename for target product
                    $pathInfo = pathinfo($sourceFile);
                    $newFileName = $pathInfo['dirname'] . '/' . 
                                 $targetProduct->getSku() . '_' . 
                                 uniqid() . '.' . 
                                 $pathInfo['extension'];

                    // Copy the image file
                    $sourcePath = $this->mediaDirectory->getAbsolutePath('catalog/product' . $sourceFile);
                    $targetPath = $this->mediaDirectory->getAbsolutePath('catalog/product' . $newFileName);

                    if ($this->mediaDirectory->isFile('catalog/product' . $sourceFile)) {
                        $this->mediaDirectory->copyFile(
                            'catalog/product' . $sourceFile,
                            'catalog/product' . $newFileName
                        );

                        // Add image to target product
                        $targetProduct->addImageToMediaGallery(
                            $targetPath,
                            ['image', 'small_image', 'thumbnail'],
                            false,
                            false
                        );

                        $copiedCount++;
                    }
                } catch (\Exception $e) {
                    // Log error but continue with other images
                    continue;
                }
            }

            return $copiedCount > 0;
        } catch (\Exception $e) {
            return false;
        }
    }

    /**
     * Get processor name
     *
     * @return string
     */
    public function getName(): string
    {
        return 'Image Processor';
    }

    /**
     * Check if processor is enabled
     *
     * @return bool
     */
    public function isEnabled(): bool
    {
        return $this->helper->isEnabled() && $this->helper->isImageProcessorEnabled();
    }
}
