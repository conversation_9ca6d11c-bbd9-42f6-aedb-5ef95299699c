<?php
declare(strict_types=1);

namespace CopeX\ProductInheritance\Model\Processor;

use CopeX\ProductInheritance\Api\InheritanceProcessorInterface;
use CopeX\ProductInheritance\Model\Config;
use Magento\Catalog\Api\Data\ProductInterface;
use Magento\Catalog\Model\Product\Gallery\Processor as GalleryProcessor;
use Magento\Framework\App\Filesystem\DirectoryList;
use Magento\Framework\Filesystem;
use Magento\Framework\Filesystem\Directory\WriteInterface;
use Magento\Framework\Filesystem\Io\File as FileIo;
use Magento\Framework\Exception\FileSystemException;
use Psr\Log\LoggerInterface;

class ImageProcessor implements InheritanceProcessorInterface
{
    private WriteInterface $mediaDirectory;

    public function __construct(
        private readonly Config $config,
        private readonly GalleryProcessor $galleryProcessor,
        private readonly FileIo $fileIo,
        private readonly LoggerInterface $logger,
        Filesystem $filesystem
    ) {
        $this->mediaDirectory = $filesystem->getDirectoryWrite(DirectoryList::MEDIA);
    }

    /**
     * Process image inheritance
     *
     * @param ProductInterface $targetProduct
     * @param ProductInterface $sourceProduct
     * @return bool
     */
    public function process(ProductInterface $targetProduct, ProductInterface $sourceProduct): bool
    {
        if (!$this->isEnabled()) {
            return false;
        }

        try {
            $sourceImages = $sourceProduct->getMediaGalleryImages();
            if (!$sourceImages || $sourceImages->getSize() === 0) {
                return false;
            }

            $copiedCount = 0;

            foreach ($sourceImages as $image) {
                if ($this->copyProductImage($image, $targetProduct)) {
                    $copiedCount++;
                }
            }

            return $copiedCount > 0;
        } catch (\Exception $e) {
            $this->logger->error(
                'Error processing image inheritance: ' . $e->getMessage(),
                ['target_product_id' => $targetProduct->getId(), 'source_product_id' => $sourceProduct->getId()]
            );
            return false;
        }
    }

    /**
     * Copy single image from source to target product
     *
     * @param mixed $image
     * @param ProductInterface $targetProduct
     * @return bool
     */
    private function copyProductImage($image, ProductInterface $targetProduct): bool
    {
        try {
            $sourceFile = $image->getFile();
            if (!$sourceFile) {
                return false;
            }

            $newFileName = $this->generateUniqueFileName($sourceFile, $targetProduct->getSku());

            if (!$this->copyImageFile($sourceFile, $newFileName)) {
                return false;
            }

            $this->addImageToProduct($targetProduct, $newFileName);
            return true;
        } catch (\Exception $e) {
            $this->logger->warning(
                'Failed to copy individual image: ' . $e->getMessage(),
                ['source_file' => $sourceFile ?? 'unknown', 'target_sku' => $targetProduct->getSku()]
            );
            return false;
        }
    }

    /**
     * Generate unique filename for copied image
     *
     * @param string $sourceFile
     * @param string $targetSku
     * @return string
     */
    private function generateUniqueFileName(string $sourceFile, string $targetSku): string
    {
        $pathInfo = $this->fileIo->getPathInfo($sourceFile);
        $directory = $pathInfo['dirname'] ?? '';
        $extension = $pathInfo['extension'] ?? '';

        return $directory . '/' . $targetSku . '_' . uniqid() . '.' . $extension;
    }

    /**
     * Copy image file from source to target location
     *
     * @param string $sourceFile
     * @param string $newFileName
     * @return bool
     * @throws FileSystemException
     */
    private function copyImageFile(string $sourceFile, string $newFileName): bool
    {
        $sourceRelativePath = 'catalog/product' . $sourceFile;
        $targetRelativePath = 'catalog/product' . $newFileName;

        if (!$this->mediaDirectory->isFile($sourceRelativePath)) {
            return false;
        }

        return $this->mediaDirectory->copyFile($sourceRelativePath, $targetRelativePath);
    }

    /**
     * Add copied image to target product
     *
     * @param ProductInterface $targetProduct
     * @param string $fileName
     * @return void
     */
    private function addImageToProduct(ProductInterface $targetProduct, string $fileName): void
    {
        $targetPath = $this->mediaDirectory->getAbsolutePath('catalog/product' . $fileName);

        $targetProduct->addImageToMediaGallery(
            $targetPath,
            ['image', 'small_image', 'thumbnail'],
            false,
            false
        );
    }

    /**
     * Get processor name
     *
     * @return string
     */
    public function getName(): string
    {
        return 'Image Processor';
    }

    /**
     * Check if processor is enabled
     *
     * @return bool
     */
    public function isEnabled(): bool
    {
        return $this->config->isEnabled() && $this->config->isImageProcessorEnabled();
    }
}
