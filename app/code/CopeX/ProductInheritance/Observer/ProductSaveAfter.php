<?php
declare(strict_types=1);

namespace CopeX\ProductInheritance\Observer;

use CopeX\ProductInheritance\Helper\Data as Helper;
use CopeX\ProductInheritance\Service\ProductInheritanceService;
use Magento\Catalog\Model\Product;
use Magento\Framework\Event\Observer;
use Magento\Framework\Event\ObserverInterface;

class ProductSaveAfter implements ObserverInterface
{
    private Helper $helper;
    private ProductInheritanceService $inheritanceService;

    public function __construct(
        Helper $helper,
        ProductInheritanceService $inheritanceService
    ) {
        $this->helper = $helper;
        $this->inheritanceService = $inheritanceService;
    }

    /**
     * Execute observer
     *
     * @param Observer $observer
     * @return void
     */
    public function execute(Observer $observer): void
    {
        if (!$this->helper->isEnabled() || !$this->helper->isAutoSyncEnabled()) {
            return;
        }

        /** @var Product $product */
        $product = $observer->getEvent()->getProduct();
        
        if (!$product || !$product->getId()) {
            return;
        }

        // Check if inherit_from_sku has been set or changed
        $inheritFromSku = $product->getData('inherit_from_sku');
        if (empty($inheritFromSku)) {
            return;
        }

        // Only process if this is not already part of an inheritance operation
        // to prevent infinite loops
        if ($product->getData('_inheritance_processing')) {
            return;
        }

        // Mark product as being processed to prevent loops
        $product->setData('_inheritance_processing', true);

        try {
            $this->inheritanceService->processProduct($product);
        } catch (\Exception $e) {
            // Log error but don't break the save process
        } finally {
            $product->unsetData('_inheritance_processing');
        }
    }
}
