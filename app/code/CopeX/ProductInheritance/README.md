# CopeX Product Inheritance Module

## Übersicht

Das CopeX Product Inheritance Modul ermöglicht es, Produktattribute basierend auf einer SKU-Referenz automatisch zu synchronisieren. Dies ist besonders nützlich für konfigurierbare Produkte, bei denen Simple Products Attribute vom Parent Product erben sollen.

## Features

- **Inherit From SKU Attribut**: Neues Produktattribut zur Angabe der Quell-SKU
- **Konfigurierbare Attribute**: Backend-Konfiguration zur Auswahl der zu synchronisierenden Attribute
- **Automatische Synchronisation**: Automatische Synchronisation beim Speichern von Produkten
- **Erweiterbare Architektur**: Plugin-System für zusätzliche Synchronisationen
- **Bulk-Synchronisation**: Console Command für Massenverarbeitung
- **Logging**: Vollständige Nachverfolgung aller Operationen

## Installation

1. Modul in `app/code/CopeX/ProductInheritance/` platzieren
2. Magento Setup ausführen:
   ```bash
   php bin/magento setup:upgrade
   php bin/magento setup:di:compile
   php bin/magento cache:flush
   ```

## Konfiguration

### Backend-Konfiguration
Navigiere zu: **Stores > Configuration > Catalog > Product Inheritance**

#### General Settings
- **Enable Product Inheritance**: Aktiviert/deaktiviert das Modul
- **Auto Sync on Product Save**: Automatische Synchronisation beim Speichern
- **Enable Logging**: Aktiviert Logging für Debugging

#### Attribute Configuration
- **Attributes to Inherit**: Auswahl der zu synchronisierenden Attribute
- **Skip Empty Values**: Leere Werte überspringen
- **Overwrite Existing Values**: Bestehende Werte überschreiben

#### Additional Processors
- **Enable Image Inheritance**: Produktbilder kopieren
- **Enable Attachment Inheritance**: Produktanhänge kopieren (Amasty)

## Verwendung

### 1. Produktattribut setzen
Im Produktbearbeitungsformular das Feld "Inherit From SKU" mit der gewünschten Quell-SKU füllen.

### 2. Automatische Synchronisation
Bei aktivierter Auto-Synchronisation werden Attribute automatisch beim Speichern übernommen.

### 3. Manuelle Synchronisation
```bash
# Alle Produkte mit inherit_from_sku synchronisieren
php bin/magento copex:product-inheritance:sync

# Bestimmte Produkt-IDs synchronisieren
php bin/magento copex:product-inheritance:sync --product-ids=1,2,3

# Dry Run (ohne Änderungen)
php bin/magento copex:product-inheritance:sync --dry-run

# Mit Batch-Größe
php bin/magento copex:product-inheritance:sync --batch-size=50
```

## Erweiterung

### Eigene Processor erstellen

1. Interface implementieren:
```php
<?php
namespace YourNamespace\YourModule\Model\Processor;

use CopeX\ProductInheritance\Api\InheritanceProcessorInterface;
use Magento\Catalog\Api\Data\ProductInterface;

class CustomProcessor implements InheritanceProcessorInterface
{
    public function process(ProductInterface $targetProduct, ProductInterface $sourceProduct): bool
    {
        // Ihre Logik hier
        return true;
    }

    public function getName(): string
    {
        return 'Custom Processor';
    }

    public function isEnabled(): bool
    {
        return true; // Oder Konfigurationswert
    }
}
```

2. In di.xml registrieren:
```xml
<type name="CopeX\ProductInheritance\Service\ProductInheritanceService">
    <arguments>
        <argument name="processors" xsi:type="array">
            <item name="custom_processor" xsi:type="object">YourNamespace\YourModule\Model\Processor\CustomProcessor</item>
        </argument>
    </arguments>
</type>
```

## Logging

Logs werden in `/var/log/copex_product_inheritance.log` gespeichert.

## Beispiel-Workflow

1. **Konfigurierbares Produkt**: SKU "SHIRT-PARENT"
2. **Simple Product 1**: SKU "SHIRT-RED-S", inherit_from_sku = "SHIRT-PARENT"
3. **Simple Product 2**: SKU "SHIRT-BLUE-M", inherit_from_sku = "SHIRT-PARENT"

Beim Speichern der Simple Products werden automatisch die konfigurierten Attribute vom Parent Product übernommen.

## Unterstützte Attribute

Standardmäßig werden folgende spezifische Attribute unterstützt:
- **subname** - Subheadline
- **description** - Description
- **short_description** - Short Description
- **detaildescription_intro** - Detailbeschreibung Einleitung
- **detailtophint** - Detail - Top Hint
- **detailtopusp1-6** - Detail - Top USP 1-6
- **listheading** - Liste Ueberschrift
- **listdescription** - Beschreibung Liste
- **listsize** - Größe Liste
- **bottom_listview** - Letzte Zeile Liste
- **meta_title** - Meta Title
- **meta_keyword** - Meta Keywords
- **meta_description** - Meta Description
- **feed_name** - Feed Name
- **feed_description** - Feed Description
- **in_feed** - In Feed
- Alle benutzerdefinierten Attribute

## Unterstützte Linked Products
- **Cross-Sells** - Verwandte Produkte
- **Up-Sells** - Zusatzverkäufe

## Technische Details

- **Magento Version**: 2.4.x
- **PHP Version**: 8.1+
- **Abhängigkeiten**: Magento_Catalog, Magento_Eav, Magento_Backend

## Support

Bei Fragen oder Problemen wenden Sie sich an das CopeX Team.
