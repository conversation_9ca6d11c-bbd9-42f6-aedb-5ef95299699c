<?php
declare(strict_types=1);

namespace CopeX\ProductInheritance\Service;

use CopeX\ProductInheritance\Api\InheritanceProcessorInterface;
use CopeX\ProductInheritance\Helper\Data as Helper;
use Magento\Catalog\Api\Data\ProductInterface;
use Magento\Catalog\Api\ProductRepositoryInterface;
use Magento\Framework\Exception\NoSuchEntityException;
use Psr\Log\LoggerInterface;

class ProductInheritanceService
{
    private ProductRepositoryInterface $productRepository;
    private Helper $helper;
    private LoggerInterface $logger;
    private array $processors;

    public function __construct(
        ProductRepositoryInterface $productRepository,
        Helper $helper,
        LoggerInterface $logger,
        array $processors = []
    ) {
        $this->productRepository = $productRepository;
        $this->helper = $helper;
        $this->logger = $logger;
        $this->processors = $processors;
    }

    /**
     * Process inheritance for a single product
     *
     * @param ProductInterface $product
     * @return bool
     */
    public function processProduct(ProductInterface $product): bool
    {
        if (!$this->helper->isEnabled()) {
            return false;
        }

        $inheritFromSku = $product->getData('inherit_from_sku');
        if (empty($inheritFromSku)) {
            return false;
        }

        try {
            $sourceProduct = $this->productRepository->get($inheritFromSku);
            return $this->inheritFromProduct($product, $sourceProduct);
        } catch (NoSuchEntityException $e) {
            $this->log("Source product with SKU '{$inheritFromSku}' not found for product ID {$product->getId()}");
            return false;
        } catch (\Exception $e) {
            $this->log("Error processing inheritance for product ID {$product->getId()}: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Process inheritance from source to target product
     *
     * @param ProductInterface $targetProduct
     * @param ProductInterface $sourceProduct
     * @return bool
     */
    public function inheritFromProduct(ProductInterface $targetProduct, ProductInterface $sourceProduct): bool
    {
        $success = true;
        $processedCount = 0;

        // Process all registered processors
        foreach ($this->processors as $processor) {
            if (!$processor instanceof InheritanceProcessorInterface) {
                continue;
            }

            if (!$processor->isEnabled()) {
                continue;
            }

            try {
                $result = $processor->process($targetProduct, $sourceProduct);
                if ($result) {
                    $processedCount++;
                    $this->log("Successfully processed {$processor->getName()} for product ID {$targetProduct->getId()}");
                } else {
                    $this->log("Failed to process {$processor->getName()} for product ID {$targetProduct->getId()}");
                }
                $success = $success && $result;
            } catch (\Exception $e) {
                $this->log("Error in {$processor->getName()} processor for product ID {$targetProduct->getId()}: " . $e->getMessage());
                $success = false;
            }
        }

        if ($processedCount > 0) {
            try {
                $this->productRepository->save($targetProduct);
                $this->log("Successfully saved product ID {$targetProduct->getId()} after inheritance processing");
            } catch (\Exception $e) {
                $this->log("Error saving product ID {$targetProduct->getId()} after inheritance: " . $e->getMessage());
                $success = false;
            }
        }

        return $success;
    }

    /**
     * Process inheritance for multiple products by their inherit_from_sku values
     *
     * @param array $productIds
     * @return array
     */
    public function processMultipleProducts(array $productIds): array
    {
        $results = [];
        
        foreach ($productIds as $productId) {
            try {
                $product = $this->productRepository->getById($productId);
                $results[$productId] = $this->processProduct($product);
            } catch (\Exception $e) {
                $this->log("Error processing product ID {$productId}: " . $e->getMessage());
                $results[$productId] = false;
            }
        }
        
        return $results;
    }

    /**
     * Get all products that have inherit_from_sku set
     *
     * @return array
     */
    public function getProductsWithInheritance(): array
    {
        // This would typically use a collection to find products with inherit_from_sku
        // For now, return empty array - will be implemented in the command
        return [];
    }

    /**
     * Log message if logging is enabled
     *
     * @param string $message
     */
    private function log(string $message): void
    {
        if ($this->helper->isLoggingEnabled()) {
            $this->logger->info('[ProductInheritance] ' . $message);
        }
    }
}
