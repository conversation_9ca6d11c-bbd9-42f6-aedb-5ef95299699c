{"name": "copex/module-product-inheritance", "description": "Product attribute inheritance based on SKU reference with configurable attributes and extensible plugin architecture", "type": "magento2-module", "license": "proprietary", "authors": [{"name": "CopeX Team", "email": "<EMAIL>"}], "version": "1.0.0", "require": {"php": "^8.1", "magento/framework": "*", "magento/module-catalog": "*", "magento/module-eav": "*", "magento/module-backend": "*"}, "autoload": {"files": ["registration.php"], "psr-4": {"CopeX\\ProductInheritance\\": ""}}}