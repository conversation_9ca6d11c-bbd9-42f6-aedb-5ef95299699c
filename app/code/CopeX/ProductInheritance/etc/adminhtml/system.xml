<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Config:etc/system_file.xsd">
    <system>
        <section id="copex_product_inheritance" translate="label" type="text" sortOrder="300" showInDefault="1" showInWebsite="1" showInStore="1">
            <label>Product Inheritance</label>
            <tab>catalog</tab>
            <resource>CopeX_ProductInheritance::config</resource>
            
            <group id="general" translate="label" type="text" sortOrder="10" showInDefault="1" showInWebsite="1" showInStore="1">
                <label>General Settings</label>
                
                <field id="enabled" translate="label" type="select" sortOrder="10" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Enable Product Inheritance</label>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                    <comment>Enable automatic attribute inheritance based on inherit_from_sku field</comment>
                </field>
                
                <field id="auto_sync" translate="label" type="select" sortOrder="20" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Auto Sync on Product Save</label>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                    <comment>Automatically sync attributes when a product is saved</comment>
                    <depends>
                        <field id="enabled">1</field>
                    </depends>
                </field>
                
                <field id="log_enabled" translate="label" type="select" sortOrder="30" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Enable Logging</label>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                    <comment>Log inheritance operations for debugging</comment>
                    <depends>
                        <field id="enabled">1</field>
                    </depends>
                </field>
            </group>
            
            <group id="attributes" translate="label" type="text" sortOrder="20" showInDefault="1" showInWebsite="1" showInStore="1">
                <label>Attribute Configuration</label>
                
                <field id="inherit_attributes" translate="label" type="multiselect" sortOrder="10" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Attributes to Inherit</label>
                    <source_model>CopeX\ProductInheritance\Model\Config\Source\ProductAttributes</source_model>
                    <comment>Select which product attributes should be inherited from the parent SKU</comment>
                    <depends>
                        <field id="copex_product_inheritance/general/enabled">1</field>
                    </depends>
                </field>
                
                <field id="exclude_empty" translate="label" type="select" sortOrder="20" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Skip Empty Values</label>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                    <comment>Skip inheritance if the source attribute value is empty</comment>
                    <depends>
                        <field id="copex_product_inheritance/general/enabled">1</field>
                    </depends>
                </field>
                
                <field id="overwrite_existing" translate="label" type="select" sortOrder="30" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Overwrite Existing Values</label>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                    <comment>Overwrite existing attribute values or only fill empty ones</comment>
                    <depends>
                        <field id="copex_product_inheritance/general/enabled">1</field>
                    </depends>
                </field>
            </group>
            
            <group id="processors" translate="label" type="text" sortOrder="30" showInDefault="1" showInWebsite="1" showInStore="1">
                <label>Additional Processors</label>
                
                <field id="enable_image_processor" translate="label" type="select" sortOrder="10" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Enable Image Inheritance</label>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                    <comment>Copy product images from parent product</comment>
                    <depends>
                        <field id="copex_product_inheritance/general/enabled">1</field>
                    </depends>
                </field>
                
                <field id="enable_attachment_processor" translate="label" type="select" sortOrder="20" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Enable Attachment Inheritance</label>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                    <comment>Copy product attachments from parent product (requires Amasty Product Attachment)</comment>
                    <depends>
                        <field id="copex_product_inheritance/general/enabled">1</field>
                    </depends>
                </field>
            </group>
        </section>
    </system>
</config>
