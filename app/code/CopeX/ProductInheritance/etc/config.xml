<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Store:etc/config.xsd">
    <default>
        <copex_product_inheritance>
            <general>
                <enabled>0</enabled>
                <auto_sync>1</auto_sync>
                <log_enabled>1</log_enabled>
            </general>
            <attributes>
                <inherit_attributes>subname,description,short_description,meta_title,meta_description</inherit_attributes>
                <exclude_empty>1</exclude_empty>
                <overwrite_existing>1</overwrite_existing>
            </attributes>
            <processors>
                <enable_image_processor>0</enable_image_processor>
                <enable_attachment_processor>0</enable_attachment_processor>
            </processors>
        </copex_product_inheritance>
    </default>
</config>
