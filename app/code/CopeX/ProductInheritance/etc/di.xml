<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">
    
    <!-- Register inheritance processors -->
    <type name="CopeX\ProductInheritance\Service\ProductInheritanceService">
        <arguments>
            <argument name="processors" xsi:type="array">
                <item name="attribute_processor" xsi:type="object">CopeX\ProductInheritance\Model\Processor\AttributeProcessor</item>
                <item name="image_processor" xsi:type="object">CopeX\ProductInheritance\Model\Processor\ImageProcessor</item>
                <item name="attachment_processor" xsi:type="object">CopeX\ProductInheritance\Model\Processor\AttachmentProcessor</item>
            </argument>
        </arguments>
    </type>

    <!-- Register console commands -->
    <type name="Magento\Framework\Console\CommandListInterface">
        <arguments>
            <argument name="commands" xsi:type="array">
                <item name="copex_product_inheritance_sync" xsi:type="object">CopeX\ProductInheritance\Console\Command\SyncProductsCommand</item>
            </argument>
        </arguments>
    </type>

    <!-- Custom logger for product inheritance -->
    <type name="CopeX\ProductInheritance\Logger\Logger">
        <arguments>
            <argument name="name" xsi:type="string">copex_product_inheritance</argument>
            <argument name="handlers" xsi:type="array">
                <item name="system" xsi:type="object">CopeX\ProductInheritance\Logger\Handler</item>
            </argument>
        </arguments>
    </type>

</config>
