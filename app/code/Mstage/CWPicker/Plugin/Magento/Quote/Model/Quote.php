<?php

namespace Mstage\CWPicker\Plugin\Magento\Quote\Model;

use Exception;
use Magento\Catalog\Model\Product;
use Mstage\CWPicker\Helper\Attribute as AttributeHelper;
use Magento\Checkout\Model\Session;
use Mstage\CWPicker\Logger\Logger;

class Quote
{
    /**
     * @var AttributeHelper
     */
    protected $attributeHelper;

    /**
     * @var Session
     */
    protected $checkoutSession;

    /**
     * @var Logger
     */
    protected $logger;

    private $called = false;

    /**
     * Quote constructor.
     * @param AttributeHelper $attributeHelper
     * @param Session $checkoutSession
     * @param Logger $logger
     */
    public function __construct(
        AttributeHelper $attributeHelper,
        Session $checkoutSession,
        Logger $logger
    ) {
        $this->attributeHelper = $attributeHelper;
        $this->checkoutSession = $checkoutSession;
        $this->logger = $logger;
    }

    /**
     * @param \Magento\Quote\Model\Quote $subject
     * @param \Magento\Quote\Model\Quote\Item $item
     * @param Product $product
     * @param $request
     * @return mixed
     * @throws Exception
     */
    public function afterAddProduct(
        \Magento\Quote\Model\Quote $subject,
        $item,
        Product $product,
        $request
    ) {
        if($this->called || !$item instanceof \Magento\Quote\Api\Data\CartItemInterface) return $item;
        $this->called = true;
        //get the earliest possible delivery date for this item
        $firstPossibleDeliveryDate = $this->attributeHelper->getFirstPossibleDeliveryDate($item, 'Y-m-d');

        //save it to the quote_item
        $item->setDeliveryDay($firstPossibleDeliveryDate);
       // $item->save(); //Not needed because this is done later by magento

        //call the refresher, that takes care of potential delivery date changes
        $this->attributeHelper->refreshQuoteItemsDeliveryDates($subject);

        $relatedParentId = $this->attributeHelper->getRelatedParentId($item);
        $item->setRelatedParentId($relatedParentId);

        return $item;
    }

    /**
     * @param \Magento\Quote\Model\Quote $subject
     * @param $result
     * @return array
     */
    public function afterGetAllVisibleItems(\Magento\Quote\Model\Quote $subject, $result) {
        return $this->attributeHelper->sortItems($result);
    }
}
