<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Config:etc/system_file.xsd">
    <system>
        <section id="xcheckout" translate="label" type="text" sortOrder="1300" showInDefault="1" showInWebsite="1" showInStore="1">
            <label>Order Delivery Date Settings</label>
            <tab>sales</tab>
            <resource>Xortex_Xcheckout::deliverydate</resource>
            <group id="general" type="text"  sortOrder="10" showInDefault="1" showInWebsite="1" showInStore="1">
                <label>Settings</label>
                <field id="format" translate="label" type="text" sortOrder="10" showInDefault="1" showInWebsite="1" showInStore="1" canRestore="1">
                    <label>Date Format</label>
                    <comment>dd.mm.yy</comment>
                </field>
                <field id="disabled" translate="label" type="multiselect" sortOrder="15" showInDefault="1" showInWebsite="1" showInStore="1" canRestore="1">
                    <label>Disabled Delivery Date</label>
                    <source_model>Xortex\Xcheckout\Model\Config\Source\Disabled</source_model>
                </field>
                <field id="offsetStart" translate="label comment" type="text" sortOrder="20" showInDefault="1" showInWebsite="1" showInStore="1" canRestore="1">
                    <label>Delivery Date Offset Start</label>
                    <comment>Offset of Startdate until today e.g. 14</comment>
                </field>
                <field id="offsetEnd" translate="label comment" type="text" sortOrder="30" showInDefault="1" showInWebsite="1" showInStore="1" canRestore="1">
                    <label>Delivery Date Offset End</label>
                    <comment>Offset of Enddate until today e.g. 44</comment>
                </field>
                <field id="cmsBlockId" translate="label comment" type="select" sortOrder="30" showInDefault="1" showInWebsite="1" showInStore="1" canRestore="1">
                    <label>Delivery Date Info Block</label>
                    <source_model>Xortex\Xbase\Model\Source\CmsBlock</source_model>
                    <comment>CMS Block with Infos about the Delivery Date</comment>
                </field>
            </group>
        </section>
    </system>
</config>
