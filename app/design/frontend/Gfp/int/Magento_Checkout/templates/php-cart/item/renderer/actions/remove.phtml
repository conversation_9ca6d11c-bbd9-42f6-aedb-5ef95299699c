<?php
/**
 * Hyvä Themes - https://hyva.io
 * Copyright © Hyvä Themes 2020-present. All rights reserved.
 * This product is licensed per Magento install
 * See https://hyva.io/license
 */

declare(strict_types=1);

use Hyva\Theme\Model\ViewModelRegistry;
use Hyva\Theme\ViewModel\HeroiconsSolid;
use Magento\Checkout\Block\Cart\Item\Renderer\Actions\Remove;
use Magento\Framework\Escaper;

/** @var Remove $block */
/** @var ViewModelRegistry $viewModels */
/** @var Escaper $escaper */

?>
<a href="#"
   title="<?= $escaper->escapeHtml(__('Remove item')) ?>"
   x-data="{}"
   @click.prevent='hyva.postForm(<?= /* @noEscape */ $block->getDeletePostJson() ?>)'
>
    <i class="fas mgz-fa-trash text-xs font-semibold"></i>
    <span class="sr-only"><?= $escaper->escapeHtml(__('Remove item')) ?></span>
</a>
