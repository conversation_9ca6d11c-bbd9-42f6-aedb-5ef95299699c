<?php
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */
use Magento\Framework\App\Action\Action;

// @codingStandardsIgnoreFile

/* @var $block \Magento\Catalog\Block\Product\AbstractProduct */
?>

<?php
$_helper = $this->helper('Magento\Catalog\Helper\Output');
$viewMode = "grid";
$showProductLink = true;
switch ($type = $block->getType()) {
    case 'related':
        /** @var \Magento\Catalog\Block\Product\ProductList\Related $block */
        if ($exist = $block->getItems()->getSize()) {
            $type = 'related';
            $class = $type;
            $viewMode = "list";

            $image = 'category_page_grid';
            $title = __('Additional Related Products');
            $items = $block->getItems();
            $limit = 0;
            $shuffle = 0;
            $canItemsAddToCart = $block->canItemsAddToCart();

            $showAddTo = false;
            $showCart = false;
            $templateType = null;
            $description = true;
            $showRelatedInfo = true;
            $showProductLink = false;
        }
    break;

    case 'other':
    break;
} ?>

<?php if ($exist):?>
    <div class="product-slider-outer product-tab-slider <?php /* @escapeNotVerified */ echo $class; ?>" data-mage-init='{"relatedProducts":{"relatedCheckbox":".related.checkbox"}}' data-limit="<?php /* @escapeNotVerified */ echo $limit; ?>" data-shuffle="<?php /* @escapeNotVerified */ echo $shuffle; ?>">
        <div class="product-slider-inner products wrapper <?php /* @escapeNotVerified */ echo $viewMode; ?> products-<?php /* @escapeNotVerified */ echo $viewMode; ?> products-<?php /* @escapeNotVerified */ echo $type; ?>">
            <h3 id="block-<?php /* @escapeNotVerified */ echo $class?>-heading" role="heading" aria-level="2"><?php /* @escapeNotVerified */ echo $title; ?></h3>
            <div class="product-slider-items slick-slider">
                <?php foreach ($items as $_item): ?>
                    <?php $_productNameStripped = $block->stripTags($_item->getName(), null, true); ?>
                    <?php $available = ''; ?>
                    <?php if (!$_item->isComposite() && $_item->isSaleable() && $type == 'related'): ?>
                        <?php if (!$_item->getRequiredOptions()): ?>
                            <?php $available = 'related-available'; ?>
                        <?php endif; ?>
                    <?php endif; ?>
                    <div class="product-slider-item">
                        <?php
                        $productImage = $block->getImage($_item, $image);
                        ?>
                        <div class="imageWrapper">
                            <?php echo $productImage->toHtml(); ?>
                        </div>
                        <div class="product title product-name-actions">
                            <strong class="product name product-item-name">
                                <?php /* @escapeNotVerified */ echo $_helper->productAttribute($_item, $_item->getName(), 'name'); ?>
                            </strong>
                        </div>

                        <div class="product product-item-price">
                            <?php /* @escapeNotVerified */ echo $block->getProductPrice($_item) ?>
                        </div>

                        <div class="product actions product-item-actions">
                            <div class="product-item-button">
                                <a onclick="showProductInfos(<?php echo $_item->getId() ?>)"
                                   title="<?php echo $_productNameStripped ?>"
                                   class="action btn-decorated">
                                    <?php echo __('more info') ?>
                                </a>
                            </div>
                            <?php if ($_item->isSaleable()): ?>
                                <div class="product-item-button">
                                    <?php $addToCartUrl  = $block->getAddToCartUrl($_item); ?>
                                    <form class="addtocart-form" data-role="tocart-form" data-product-sku="<?= $block->escapeHtml($_item->getSku()) ?>" action="<?= $addToCartUrl ?>" method="post">
                                        <?= $block->getBlockHtml('formkey') ?>
                                        <button type="submit"
                                                title="<?= $block->escapeHtml(__('Add to Cart')) ?>"
                                                class="action btn-main">
                                            <span><?= /* @escapeNotVerified */ __('Add to Cart') ?></span>
                                        </button>
                                    </form>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                <?php endforeach ?>
            </div>
        </div>
    </div>

    <script type="text/javascript">
        requirejs(["jquery","slick"],
            function($) {
                var related = $('.product-slider-items');
                related.slick({
                    infinite: false,
                    slidesToShow: 4,
                    autoplay: false,
                    arrow: true,
                    dots: false,
                    lazyLoad: 'ondemand',
                    adaptiveHeight: true,
                    responsive: [
                        {
                            breakpoint: 991,
                            settings: {
                                slidesToShow: 3
                            }
                        },
                        {
                            breakpoint: 767,
                            settings: {
                                slidesToShow: 2
                            }
                        },
                        {
                            breakpoint: 480,
                            settings: {
                                slidesToShow: 1
                            }
                        }
                    ]
                });
            }
        );
    </script>
<?php endif;?>
