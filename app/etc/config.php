<?php
return [
    'modules' => [
        'Magento_AdminAnalytics' => 1,
        'Magento_Store' => 1,
        'Magento_Directory' => 1,
        'Magento_Amqp' => 1,
        'Magento_Config' => 1,
        'Magento_Theme' => 1,
        'Magento_Variable' => 1,
        'Magento_Backend' => 1,
        'Magento_Backup' => 1,
        'Magento_Eav' => 1,
        'Magento_Customer' => 1,
        'Magento_CacheInvalidate' => 1,
        'Magento_AdminNotification' => 1,
        'Magento_Indexer' => 1,
        'Magento_Authorization' => 1,
        'Magento_GraphQl' => 1,
        'Magento_CatalogImportExport' => 1,
        'Magento_Cms' => 1,
        'Magento_Rule' => 1,
        'Magento_Catalog' => 1,
        'Magento_CatalogRuleGraphQl' => 1,
        'Magento_CatalogRule' => 1,
        'Magento_CatalogUrlRewrite' => 1,
        'Magento_EavGraphQl' => 1,
        'Magento_Widget' => 1,
        'Magento_Quote' => 1,
        'Magento_SalesSequence' => 1,
        'Magento_Bundle' => 1,
        'Magento_CmsUrlRewrite' => 1,
        'Magento_StoreGraphQl' => 1,
        'Magento_User' => 1,
        'Magento_Msrp' => 1,
        'Magento_Payment' => 1,
        'Magento_Search' => 1,
        'Magento_CatalogInventory' => 1,
        'Magento_Contact' => 1,
        'Magento_Cookie' => 1,
        'Magento_Cron' => 1,
        'Magento_Csp' => 1,
        'Magento_CurrencySymbol' => 1,
        'Magento_CatalogGraphQl' => 1,
        'Magento_CustomerGraphQl' => 1,
        'Magento_CustomerImportExport' => 1,
        'Magento_Deploy' => 1,
        'Magento_Developer' => 1,
        'Magento_QuoteGraphQl' => 1,
        'Magento_DirectoryGraphQl' => 1,
        'Magento_Downloadable' => 1,
        'Magento_DownloadableGraphQl' => 1,
        'Magento_Sales' => 1,
        'Magento_UrlRewriteGraphQl' => 1,
        'Magento_CatalogSearch' => 1,
        'Magento_AdvancedSearch' => 1,
        'Magento_Email' => 1,
        'Magento_EncryptionKey' => 1,
        'Magento_GiftMessage' => 1,
        'Magento_GiftMessageGraphQl' => 1,
        'Magento_GoogleAnalytics' => 1,
        'Magento_Checkout' => 1,
        'Magento_CompareListGraphQl' => 1,
        'Magento_PageCache' => 1,
        'Magento_GroupedProduct' => 1,
        'Magento_GroupedCatalogInventory' => 1,
        'Magento_GroupedProductGraphQl' => 1,
        'Magento_ImportExport' => 1,
        'Magento_MediaStorage' => 1,
        'Magento_InstantPurchase' => 1,
        'Magento_Security' => 1,
        'Magento_Inventory' => 0,
        'Magento_InventoryAdminUi' => 0,
        'Magento_InventoryAdvancedCheckout' => 0,
        'Magento_InventoryApi' => 0,
        'Magento_InventoryBundleImportExport' => 0,
        'Magento_InventoryBundleProduct' => 0,
        'Magento_InventoryBundleProductAdminUi' => 0,
        'Magento_InventoryBundleProductIndexer' => 0,
        'Magento_InventoryCatalog' => 0,
        'Magento_InventorySales' => 0,
        'Magento_InventoryCatalogAdminUi' => 0,
        'Magento_InventoryCatalogApi' => 0,
        'Magento_InventoryCatalogFrontendUi' => 0,
        'Magento_InventoryCatalogSearch' => 0,
        'Magento_InventoryCatalogSearchBundleProduct' => 0,
        'Magento_InventoryCatalogSearchConfigurableProduct' => 0,
        'Magento_ConfigurableProduct' => 1,
        'Magento_ConfigurableProductGraphQl' => 1,
        'Magento_InventoryConfigurableProduct' => 0,
        'Magento_InventoryConfigurableProductIndexer' => 0,
        'Magento_InventoryConfiguration' => 0,
        'Magento_InventoryConfigurationApi' => 0,
        'Magento_InventoryDistanceBasedSourceSelection' => 0,
        'Magento_InventoryDistanceBasedSourceSelectionAdminUi' => 0,
        'Magento_InventoryDistanceBasedSourceSelectionApi' => 0,
        'Magento_InventoryElasticsearch' => 0,
        'Magento_InventoryExportStockApi' => 0,
        'Magento_InventoryIndexer' => 0,
        'Magento_InventorySalesApi' => 0,
        'Magento_InventoryGroupedProduct' => 0,
        'Magento_InventoryGroupedProductAdminUi' => 0,
        'Magento_InventoryGroupedProductIndexer' => 0,
        'Magento_InventoryImportExport' => 0,
        'Magento_InventoryInStorePickupApi' => 0,
        'Magento_Ui' => 1,
        'Magento_InventorySourceSelectionApi' => 0,
        'Magento_InventoryInStorePickup' => 0,
        'Magento_InventoryInStorePickupGraphQl' => 0,
        'Magento_Shipping' => 1,
        'Magento_InventoryInStorePickupShippingApi' => 0,
        'Magento_InventoryInStorePickupQuoteGraphQl' => 0,
        'Magento_InventoryInStorePickupSales' => 0,
        'Magento_InventoryInStorePickupSalesApi' => 0,
        'Magento_InventoryInStorePickupQuote' => 0,
        'Magento_InventoryInStorePickupShipping' => 0,
        'Magento_InventoryInStorePickupShippingAdminUi' => 0,
        'Magento_Multishipping' => 1,
        'Magento_Integration' => 1,
        'Magento_InventoryCache' => 0,
        'Magento_InventoryLowQuantityNotification' => 0,
        'Magento_Reports' => 1,
        'Magento_InventoryLowQuantityNotificationApi' => 0,
        'Magento_InventoryMultiDimensionalIndexerApi' => 0,
        'Magento_InventoryProductAlert' => 0,
        'Magento_InventoryQuoteGraphQl' => 0,
        'Magento_InventoryRequisitionList' => 0,
        'Magento_InventoryReservations' => 0,
        'Magento_InventoryReservationCli' => 0,
        'Magento_InventoryReservationsApi' => 0,
        'Magento_InventoryExportStock' => 0,
        'Magento_InventorySalesAdminUi' => 0,
        'Magento_InventoryGraphQl' => 0,
        'Magento_InventorySalesAsyncOrder' => 1,
        'Magento_InventorySalesFrontendUi' => 0,
        'Magento_InventorySetupFixtureGenerator' => 0,
        'Magento_InventoryShipping' => 0,
        'Magento_InventoryShippingAdminUi' => 0,
        'Magento_InventorySourceDeductionApi' => 0,
        'Magento_InventorySourceSelection' => 0,
        'Magento_InventoryInStorePickupFrontend' => 0,
        'Magento_InventorySwatchesFrontendUi' => 0,
        'Magento_InventoryVisualMerchandiser' => 0,
        'Magento_InventoryWishlist' => 0,
        'Magento_JwtFrameworkAdapter' => 1,
        'Magento_JwtUserToken' => 1,
        'Magento_LayeredNavigation' => 1,
        'Magento_LoginAsCustomer' => 1,
        'Magento_LoginAsCustomerAdminUi' => 1,
        'Magento_LoginAsCustomerApi' => 1,
        'Magento_LoginAsCustomerAssistance' => 1,
        'Magento_LoginAsCustomerFrontendUi' => 1,
        'Magento_LoginAsCustomerGraphQl' => 1,
        'Magento_LoginAsCustomerLog' => 1,
        'Magento_LoginAsCustomerPageCache' => 1,
        'Magento_LoginAsCustomerQuote' => 1,
        'Magento_LoginAsCustomerSales' => 1,
        'Magento_MediaContent' => 1,
        'Magento_MediaContentApi' => 1,
        'Magento_MediaContentCatalog' => 1,
        'Magento_MediaContentCms' => 1,
        'Magento_MediaContentSynchronization' => 1,
        'Magento_MediaContentSynchronizationApi' => 1,
        'Magento_MediaContentSynchronizationCatalog' => 1,
        'Magento_MediaContentSynchronizationCms' => 1,
        'Magento_MediaGallery' => 1,
        'Magento_MediaGalleryApi' => 1,
        'Magento_MediaGalleryCatalog' => 1,
        'Magento_MediaGalleryCatalogIntegration' => 1,
        'Magento_MediaGalleryCatalogUi' => 1,
        'Magento_MediaGalleryCmsUi' => 1,
        'Magento_MediaGalleryIntegration' => 1,
        'Magento_MediaGalleryMetadata' => 1,
        'Magento_MediaGalleryMetadataApi' => 1,
        'Magento_MediaGalleryRenditions' => 1,
        'Magento_MediaGalleryRenditionsApi' => 1,
        'Magento_MediaGallerySynchronization' => 1,
        'Magento_MediaGallerySynchronizationApi' => 1,
        'Magento_MediaGallerySynchronizationMetadata' => 1,
        'Magento_MediaGalleryUi' => 1,
        'Magento_MediaGalleryUiApi' => 1,
        'Magento_Robots' => 1,
        'Magento_MessageQueue' => 1,
        'Magento_CatalogRuleConfigurable' => 1,
        'Magento_MsrpConfigurableProduct' => 1,
        'Magento_MsrpGroupedProduct' => 1,
        'Magento_InventoryInStorePickupMultishipping' => 0,
        'Magento_MysqlMq' => 1,
        'Magento_NewRelicReporting' => 0,
        'Magento_Newsletter' => 1,
        'Magento_NewsletterGraphQl' => 1,
        'Magento_OfflinePayments' => 1,
        'Magento_SalesRule' => 1,
        'Magento_Elasticsearch' => 1,
        'Magento_CatalogWidget' => 1,
        'Magento_GraphQlCache' => 1,
        'Magento_Captcha' => 1,
        'Magento_PaymentGraphQl' => 1,
        'Magento_Vault' => 1,
        'Magento_Paypal' => 1,
        'Magento_Persistent' => 1,
        'Magento_ProductAlert' => 1,
        'Magento_ProductVideo' => 1,
        'Magento_CheckoutAgreements' => 1,
        'Magento_QuoteBundleOptions' => 1,
        'Magento_QuoteConfigurableOptions' => 1,
        'Magento_QuoteDownloadableLinks' => 1,
        'Magento_InventoryConfigurableProductAdminUi' => 0,
        'Magento_ReCaptchaAdminUi' => 1,
        'Magento_ReCaptchaCheckout' => 1,
        'Magento_ReCaptchaCheckoutSalesRule' => 1,
        'Magento_ReCaptchaContact' => 1,
        'Magento_ReCaptchaCustomer' => 1,
        'Magento_ReCaptchaFrontendUi' => 1,
        'Magento_ReCaptchaMigration' => 1,
        'Magento_ReCaptchaNewsletter' => 1,
        'Magento_ReCaptchaPaypal' => 1,
        'Magento_ReCaptchaReview' => 1,
        'Magento_ReCaptchaSendFriend' => 1,
        'Magento_ReCaptchaStorePickup' => 1,
        'Magento_ReCaptchaUi' => 1,
        'Magento_ReCaptchaUser' => 1,
        'Magento_ReCaptchaValidation' => 1,
        'Magento_ReCaptchaValidationApi' => 1,
        'Magento_ReCaptchaVersion2Checkbox' => 1,
        'Magento_ReCaptchaVersion2Invisible' => 1,
        'Magento_ReCaptchaVersion3Invisible' => 1,
        'Magento_ReCaptchaWebapiApi' => 1,
        'Magento_ReCaptchaWebapiGraphQl' => 1,
        'Magento_ReCaptchaWebapiRest' => 1,
        'Magento_ReCaptchaWebapiUi' => 1,
        'Magento_ReCaptchaWishlist' => 1,
        'Magento_RelatedProductGraphQl' => 1,
        'Magento_ReleaseNotification' => 1,
        'Magento_Sitemap' => 1,
        'Magento_InventoryLowQuantityNotificationAdminUi' => 0,
        'Magento_RequireJs' => 1,
        'Magento_Review' => 1,
        'Magento_ReviewGraphQl' => 1,
        'Magento_RemoteStorage' => 1,
        'Magento_Rss' => 1,
        'Magento_Elasticsearch7' => 1,
        'Magento_ConfigurableProductSales' => 1,
        'Magento_SalesGraphQl' => 1,
        'Magento_SalesInventory' => 1,
        'Magento_OfflineShipping' => 1,
        'Magento_ConfigurableImportExport' => 1,
        'Magento_OpenSearch' => 1,
        'Magento_Webapi' => 1,
        'Magento_Securitytxt' => 1,
        'Magento_SendFriend' => 1,
        'Magento_InventoryInStorePickupSalesAdminUi' => 0,
        'Magento_AwsS3' => 1,
        'Magento_InventoryConfigurableProductFrontendUi' => 1,
        'Magento_BundleGraphQl' => 1,
        'Magento_Swatches' => 1,
        'Magento_SwatchesGraphQl' => 1,
        'Magento_SwatchesLayeredNavigation' => 1,
        'Magento_Tax' => 1,
        'Magento_CatalogCustomerGraphQl' => 1,
        'Magento_Translation' => 1,
        'Magento_InventoryInStorePickupAdminUi' => 0,
        'Magento_UrlRewrite' => 1,
        'Magento_CatalogUrlRewriteGraphQl' => 1,
        'Magento_AsynchronousOperations' => 1,
        'Magento_GoogleGtag' => 0,
        'Magento_PaypalCaptcha' => 1,
        'Magento_InventoryInStorePickupWebapiExtension' => 0,
        'Magento_WebapiAsync' => 1,
        'Magento_WebapiSecurity' => 1,
        'Magento_Weee' => 1,
        'Magento_PageBuilder' => 1,
        'Magento_Wishlist' => 1,
        'Adyen_Payment' => 1,
        'Amasty_AdvancedReview' => 1,
        'Amasty_Base' => 1,
        'Hyva_Theme' => 1,
        'Amasty_AdvancedReviewLite' => 1,
        'Amasty_AdvancedReviewSubscriptionPackage' => 1,
        'Amasty_AdvancedReviewApi' => 1,
        'Amasty_CronScheduleList' => 0,
        'Amasty_CustomerAttributes' => 1,
        'Amasty_Feed' => 1,
        'Amasty_Fpc' => 1,
        'Hyva_CompatModuleFallback' => 1,
        'Amasty_Geoip' => 1,
        'Amasty_GeoipRedirect' => 1,
        'Amasty_Hyva' => 1,
        'Amasty_InvisibleCaptcha' => 1,
        'Amasty_Label' => 1,
        'Amasty_Oaction' => 1,
        'Amasty_Orderarchive' => 1,
        'Amasty_Orderattr' => 1,
        'Amasty_ProductAttachment' => 1,
        'Amasty_Pgrid' => 1,
        'Amasty_ProductLabelsHyva' => 1,
        'Amasty_ProductLabelsSubscriptionPack' => 1,
        'Amasty_ReviewPageBuilder' => 1,
        'Amasty_ThankYouPage' => 1,
        'Amazon_Pay' => 1,
        'AvS_ScopeHint' => 1,
        'Awin_AdvertiserTracking' => 0,
        'Catgento_AdminActivity' => 1,
        'CleverReach_CleverReachIntegration' => 1,
        'Cobby_Connector' => 1,
        'CopeX_APIAddStockInfoToProduct' => 1,
        'CopeX_Admin' => 1,
        'Smile_ElasticsuiteCore' => 1,
        'CopeX_AllTopProductOptions' => 1,
        'CopeX_AmastyExtraEmailAttachments' => 1,
        'CopeX_AmastyGeoIpDefaultCountry' => 1,
        'CopeX_AmastyGeoIpVarnish' => 1,
        'CopeX_AmastyGeoipAutoDBUpdate' => 1,
        'CopeX_AmastyGridClearCache' => 1,
        'CopeX_AmastyReviews' => 1,
        'CopeX_AmasytInFeedFixTemporary' => 1,
        'CopeX_AutoRefreshCache' => 1,
        'CopeX_AwinCheckoutFix' => 1,
        'CopeX_CartDiscountPercentage' => 1,
        'CopeX_CatalogImageResizePerTheme' => 1,
        'CopeX_CatalogSearchSpamFilter' => 1,
        'CopeX_CategoryAttribute' => 1,
        'CopeX_CategoryShippingInfo' => 1,
        'CopeX_CheckInvalidCache' => 0,
        'CopeX_CheckoutPaymentErrorDiscount' => 1,
        'CopeX_CheckoutSuccessAdditionalInfo' => 1,
        'CopeX_Cleanup' => 1,
        'CopeX_Core' => 1,
        'CopeX_CreateFrenchWebsite' => 1,
        'CopeX_DefaultShippingCountry' => 1,
        'Hyva_ThemeFallback' => 1,
        'CopeX_DisableSpecialPriceFromDate' => 1,
        'CopeX_ElasticSuiteRelatedParentProducts' => 1,
        'Smile_ElasticsuiteCatalog' => 1,
        'CopeX_EmailBlacklist' => 1,
        'CopeX_ExitIntent' => 1,
        'MagePal_GoogleTagManager' => 1,
        'Hyva_MagePalGoogleTagManager' => 1,
        'CopeX_HyvaAjaxAddToCart' => 1,
        'CopeX_HyvaTheme' => 1,
        'CopeX_HyvaVideo' => 1,
        'Yireo_CspUtilities' => 1,
        'CopeX_InlineFontLoader' => 1,
        'CopeX_KlarnaIncrementIdFix' => 1,
        'CopeX_LandingPageParameter' => 1,
        'CopeX_PRG' => 1,
        'CopeX_ProductDetailLazyLoad' => 1,
        'CopeX_ProductDetailSeoLink' => 1,
        'CopeX_ProductInheritance' => 1,
        'CopeX_ProductViewBreadcrumbs' => 1,
        'CopeX_ResourceHints' => 1,
        'CopeX_ReviewTitle' => 1,
        'CopeX_ServersideGclid' => 1,
        'CopeX_ShippingInfo' => 1,
        'CopeX_SwatchesUpdates' => 1,
        'CopeX_Swiper' => 1,
        'CopeX_TaxInfo' => 1,
        'Xtento_XtCore' => 1,
        'CtiDigital_CspWhitelist' => 1,
        'Door4_CustomerPrefixPatch' => 1,
        'Emipro_Apichange' => 1,
        'EthanYehuda_CronjobManager' => 1,
        'Experius_EmailCatcher' => 1,
        'FME_Photogallery' => 1,
        'Fooman_EmailAttachments' => 1,
        'Smile_ElasticsuiteSwatches' => 1,
        'GFP_Csp' => 1,
        'GFP_CsrfErrorFix' => 1,
        'GFP_Seo' => 1,
        'GFP_Theme' => 1,
        'Hackathon_EAVCleaner' => 1,
        'Hyva_AmazonPay' => 1,
        'Hyva_CmsTailwindJit' => 1,
        'Amasty_AdvancedReviewHyva' => 0,
        'Hyva_Email' => 1,
        'Hyva_GraphqlTokens' => 1,
        'Hyva_GraphqlViewModel' => 1,
        'Hyva_LumaCheckout' => 1,
        'MagePal_Core' => 1,
        'CopeX_GoogleTagManager' => 1,
        'Hyva_MagentoCoreFixes' => 1,
        'Magezon_Core' => 1,
        'Hyva_MollieThemeBundle' => 0,
        'Hyva_OrderCancellationWebapi' => 1,
        'Hyva_PageBuilder' => 1,
        'Hyva_PaymentIcons' => 1,
        'Hyva_SmileElasticsuite' => 1,
        'Amasty_FpcHyvaCompatibility' => 1,
        'CopeX_DisableCriticalCssOnThemeFallback' => 1,
        'IntegerNet_GlobalCustomLayout' => 1,
        'Jajuma_HyvaFlags' => 1,
        'Klarna_Base' => 1,
        'Klarna_Backend' => 1,
        'Klarna_Kco' => 1,
        'Klarna_Keb' => 1,
        'Klarna_Kp' => 1,
        'Klarna_KpGraphQl' => 1,
        'Klarna_Kss' => 1,
        'Klarna_Logger' => 1,
        'Klarna_Onsitemessaging' => 1,
        'Klarna_Orderlines' => 1,
        'Klarna_Support' => 1,
        'MSP_Common' => 0,
        'MSP_DevTools' => 0,
        'MagePal_GoogleAnalytics4' => 1,
        'Hyva_MagePalGoogleAnalytics4' => 1,
        'CopeX_GTMOrderAddressOnSuccess' => 1,
        'MageWorx_OpenAI' => 1,
        'MageWorx_SeoAll' => 1,
        'MageWorx_Info' => 1,
        'MageWorx_GoogleAI' => 1,
        'MageWorx_SeoAI' => 1,
        'MageWorx_HtmlSitemap' => 1,
        'MageWorx_SeoBase' => 1,
        'MageWorx_SeoBreadcrumbs' => 1,
        'MageWorx_SeoCategoryGrid' => 1,
        'MageWorx_SeoCrossLinks' => 1,
        'MageWorx_SeoExtended' => 1,
        'MageWorx_SeoMarkup' => 1,
        'MageWorx_SeoRedirects' => 1,
        'MageWorx_SeoReports' => 1,
        'MageWorx_SeoUrls' => 1,
        'MageWorx_SeoXTemplates' => 1,
        'MageWorx_XmlSitemap' => 1,
        'Magefan_WysiwygAdvanced' => 1,
        'Magezon_Builder' => 1,
        'Magezon_PageBuilder' => 1,
        'Hyva_MagezonPageBuilder' => 1,
        'Magezon_PageBuilderIconBox' => 1,
        'Magezon_PageBuilderPageableContainer' => 1,
        'Magezon_PageBuilderPreview' => 1,
        'Mollie_Payment' => 1,
        'Mollie_HyvaCompatibility' => 0,
        'Mstage_CWPicker' => 1,
        'Mstage_CheckoutBlockProvider' => 1,
        'Mstage_ExtendConfigurable' => 1,
        'Mstage_SubscribeForCleverreach' => 1,
        'OlegKoval_RegenerateUrlRewrites' => 1,
        'Payolution_Payments' => 1,
        'Payone_Core' => 0,
        'RedChamps_CleanMenu' => 1,
        'ReesSolutions_DBOverride' => 1,
        'Salecto_MediaStorageSync' => 1,
        'Smile_ElasticsuiteAdminNotification' => 1,
        'Smile_ElasticsuiteTracker' => 1,
        'GFP_CatalogLeftnavDisplay' => 1,
        'Smile_ElasticsuiteCatalogGraphQl' => 1,
        'Smile_ElasticsuiteCatalogRule' => 1,
        'Smile_ElasticsuiteVirtualCategory' => 1,
        'Smile_ElasticsuiteCatalogOptimizer' => 1,
        'Smile_ElasticsuiteThesaurus' => 1,
        'CopeX_AdvancedElasticsuiteCatalog' => 1,
        'Smile_ElasticsuiteIndices' => 1,
        'Smile_ElasticsuiteAnalytics' => 1,
        'CopeX_ElasticsuitePossibleManualSortingFix' => 1,
        'Temando_ShippingRemover' => 1,
        'Tigren_AdvancedCheckout' => 1,
        'Trustedshops_Trustedshops' => 1,
        'Unzer_PAPI' => 1,
        'Xortex_Xcheckout' => 1,
        'Xortex_Xbase' => 1,
        'Xortex_App' => 1,
        'Xortex_Xproducttabs' => 1,
        'Xtento_OrderExport' => 1,
        'Xtento_ProductExport' => 1,
        'CopeX_XtentoMultipleExportItems' => 1,
        'Yireo_NextGenImages' => 1,
        'CopeX_ImageTagNextgen' => 1,
        'Yireo_Webp2' => 1,
        'Yireo_Whoops' => 0
    ]
];
