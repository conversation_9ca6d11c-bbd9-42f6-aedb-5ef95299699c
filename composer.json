{"name": "magento/project-community-edition", "description": "eCommerce Platform for Growth (Community Edition)", "type": "project", "version": "2.4.2", "license": ["OSL-3.0", "AFL-3.0"], "require": {"magento/product-community-edition": "2.4.6-p9", "clever/magento2": "*", "trustedshops/trustedshops": "*", "magepal/magento2-googletagmanager": "^2.7", "payone-gmbh/magento-2": "^3.2", "olegkoval/magento2-regenerate-url-rewrites": "dev-master", "amasty/feed": "^2.5.2", "amasty/product-attachment": "^2.3", "amasty/orderattr": "*", "amasty/module-thank-you-page": "^1.1", "amasty/module-customer-attributes": "*", "amasty/module-mass-order-actions": "^1.5.5", "amasty/orderarchive": "^1.1", "amasty/geoipredirect": "^1.3", "amasty/pgrid": "^1.12", "amasty/advanced-review": "*", "cweagans/composer-patches": "^1.7", "avstudnitz/scopehint2": "^1", "splendidinternet/mage2-locale-de-de": "^1.66", "experius/module-emailcatcher": "^4.0", "ethanyehuda/magento2-cronjobmanager": "^v1.9.1", "door4/magento2-customer-prefix-patch": "dev-master", "fooman/emailattachments-m2": "^3.3", "magento/composer-root-update-plugin": "~2.0", "magento/composer-dependency-version-audit-plugin": "~0.1", "awin/module-advertisertracking": "^1.0", "hyva-themes/magento2-theme-module": "1.3.*", "hyva-themes/magento2-reset-theme": "^1.1", "hyva-themes/magento2-graphql-tokens": "^1.0", "hyva-themes/magento2-email-module": "^1.0", "hyva-themes/magento2-default-theme": "^1.3", "hyva-themes/magento2-compat-module-fallback": "^1.1", "hyva-themes/magento2-theme-fallback": "^1.0", "hyva-themes/magento2-luma-checkout": "^1.1", "amzn/amazon-pay-magento-2-module": "^5.9", "catgento/module-admin-activity": "^1", "hyva-themes/magento2-magepal-googletagmanager": "^1.0", "hyva-themes/magento2-page-builder": "^1.0", "yireo/magento2-webp2": "^0.12.5", "magento-hackathon/module-eavcleaner-m2": "^1.2", "xtento/orderexport": "^2.15", "xtento/productexport": "^2.14", "klarna/m2-klarna": "^1.0", "mollie/magento2": "^2.16", "magefan/module-wysiwyg-advanced": "^2.0", "mageworx/module-seosuiteultimate": "^2.38.3", "smile/elasticsuite": "~2.11.0", "hyva-themes/magento2-smile-elasticsuite": "^1.1", "integer-net/magento2-global-custom-layout": "^1.1", "copex/module-prg": "^1.0", "magepal/magento2-google-analytics4": "^1.7", "hyva-themes/magento2-magepal-google-analytics4": "^1.0", "amasty/module-fpc-hyva-compatibility": "^1.0", "cobbyio/cobby-connector-magento2": "^2.4", "reessolutions/db-override": "^1.0", "amasty/module-product-labels-subscription-pack": "^2.3", "amasty/module-product-labels-hyva": "^2.0.2", "amasty/module-fpc-warmer": "^2.9.1", "unzerdev/magento2": "^3.2", "copex/module-landing-page-parameter": "^1.0", "amasty/module-advanced-review-subscription-package": "^1.17", "amasty/advanced-reviews-page-builder": "^1.0", "amasty/module-advanced-review-api": "^1.0", "amasty/module-advanced-review-hyva": "^1.0", "copex/module-hyvavideo": "^1.0", "jajuma/hyva-flags": "^1.0", "hyva-themes/magento2-cms-tailwind-jit": "^1.1", "hyva-themes/magento2-payment-icons": "^2.0", "redchamps/module-clean-admin-menu": "^1.1", "adyen/module-payment": "^9.12", "perspective/magento2-partytown": "dev-master"}, "require-dev": {"yireo/magento2-whoops": "*", "mage2tv/magento-cache-clean": "*", "lusitanian/oauth": "~0.8.10", "pdepend/pdepend": "2.*", "deployer/deployer": "^7.0", "msp/devtools": "^1.3", "magento/magento-coding-standard": "*", "salecto2/magento2-mediastoragesync": "^1.0"}, "replace": {"magento/module-sampledata": "*", "shopialfb/facebook-module": "*", "dotmailer/dotmailer-magento2-extension": "*", "dotmailer/dotmailer-magento2-extension-package": "*", "dotmailer/dotmailer-magento2-extension-enterprise": "*", "dotmailer/dotmailer-magento2-extension-chat": "*", "vertex/product-magento-module": "*", "vertex/module-tax": "*", "vertex/sdk": "*", "yotpo/magento2-module-yotpo-reviews": "*", "yotpo/magento2-module-yotpo-reviews-bundle": "*", "magento/google-shopping-ads": "*", "magento/module-advanced-pricing-import-export": "*", "magento/module-analytics": "*", "magento/module-authorizenet": "*", "magento/module-authorizenet-acceptjs": "*", "magento/module-authorizenet-cardinal": "*", "magento/module-bundle-import-export": "*", "magento/module-catalog-analytics": "*", "magento/module-cardinal-commerce": "*", "magento/module-customer-analytics": "*", "magento/module-cybersource": "*", "magento/module-dhl": "*", "magento/module-downloadable-import-export": "*", "magento/module-eway": "*", "magento/module-fedex": "*", "magento/module-google-adwords": "*", "magento/module-google-optimizer": "*", "magento/module-grouped-import-export": "*", "magento/module-marketplace": "*", "magento/module-quote-analytics": "*", "magento/module-review-analytics": "*", "magento/module-sales-analytics": "*", "magento/module-sample-data": "*", "magento/module-signifyd": "*", "magento/module-swagger": "*", "magento/module-swagger-webapi": "*", "magento/module-swagger-webapi-async": "*", "magento/module-tax-import-export": "*", "magento/module-ups": "*", "magento/module-usps": "*", "magento/module-version": "*", "magento/module-wishlist-analytics": "*", "magento/module-worldpay": "*", "eadesignro/module-eacore": "*", "magento/module-authorizenet-graph-ql": "*", "magento/module-braintree-graph-ql": "*", "magento/module-catalog-cms-graph-ql": "*", "magento/module-catalog-customer-ql": "*", "magento/module-catalog-inventory-graph-ql": "*", "magento/module-checkout-agreements-graph-ql": "*", "magento/module-cms-graph-ql": "*", "magento/module-cms-url-rewrite-graph-ql": "*", "magento/module-customer-balance-graph-ql": "*", "magento/module-customer-downloadable-graph-ql": "*", "magento/module-gift-card-account-graph-ql": "*", "magento/module-gift-card-graph-ql": "*", "magento/module-paypal-graph-ql": "*", "magento/module-reward-graph-ql": "*", "magento/module-rma-graph-ql": "*", "magento/module-send-friend-graph-ql": "*", "magento/module-tax-graph-ql": "*", "magento/module-theme-graph-ql": "*", "magento/module-vault-graph-ql": "*", "magento/module-weee-graph-ql": "*", "magento/module-wishlist-graph-ql": "*", "vertexinc/product-magento-module": "*", "magento/module-page-builder-analytics": "*", "magento/module-page-builder-admin-analytics": "*", "magento/module-catalog-page-builder-analytics": "*", "braintree/braintree_php": "*", "paypal/module-braintree": "*", "magefan/module-admin-user-guide": "*", "magento/module-adobe-ims": "*", "magento/module-admin-adobe-ims": "*", "magento/module-adobe-ims-api": "*", "magento/adobe-stock-integration": "*", "magento/module-adobe-stock-admin-ui": "*", "magento/module-adobe-stock-asset": "*", "magento/module-adobe-stock-asset-api": "*", "magento/module-adobe-stock-client": "*", "magento/module-adobe-stock-client-api": "*", "magento/module-adobe-stock-image": "*", "magento/module-adobe-stock-image-admin-ui": "*", "magento/module-adobe-stock-image-api": "*", "magento/module-admin-adobe-ims-two-factor-auth": "*", "magento/module-two-factor-auth": "*", "magento/module-aws-s3-page-builder": "*", "magento/module-cms-page-builder-analytics": "*", "magefan/module-community": "*"}, "autoload": {"psr-4": {"Magento\\Framework\\": "lib/internal/Magento/Framework/", "Magento\\Setup\\": "setup/src/Magento/Setup/", "Magento\\": "app/code/Magento/"}, "psr-0": {"": "app/code/"}, "files": ["app/etc/NonComposerComponentRegistration.php"], "exclude-from-classmap": ["**/dev/**", "**/update/**", "**/Test/**", ".giti<PERSON>re"]}, "autoload-dev": {"psr-4": {"Magento\\Sniffs\\": "dev/tests/static/framework/Magento/Sniffs/", "Magento\\Tools\\": "dev/tools/Magento/Tools/", "Magento\\Tools\\Sanity\\": "dev/build/publication/sanity/Magento/Tools/Sanity/", "Magento\\TestFramework\\Inspection\\": "dev/tests/static/framework/Magento/TestFramework/Inspection/", "Magento\\TestFramework\\Utility\\": "dev/tests/static/framework/Magento/TestFramework/Utility/"}}, "minimum-stability": "stable", "repositories": {"mageworx_packages": {"type": "composer", "url": "https://packages.mageworx.com/"}, "private-packagist": {"type": "composer", "url": "https://hyva-themes.repo.packagist.com/gfp-international/"}, "magepal": {"type": "composer", "url": "https://composer.magepal.com/"}, "xtento": {"type": "composer", "url": "https://repo.xtento.com"}, "copex": {"type": "composer", "url": "https://repo.copex.io/gfp-b806635d44b7d5ca64a696c4ec9a92d2/"}, "copex2": {"type": "composer", "url": "https://packages.copex.io/"}, "magento": {"type": "composer", "url": "https://repo.magento.com/", "canonical": false}, "prefix-patch": {"type": "vcs", "url": "https://github.com/DanBeckett/door4-customer-prefix-patch"}, "add-to-cart-graphql": {"type": "vcs", "url": "https://github.com/CopeX/magento2-addtocartgraphql.git"}, "cache-warmer": {"type": "vcs", "url": "https://github.com/CopeX/module-cache-warmer.git"}, "devtools": {"type": "vcs", "url": "https://github.com/CopeX/m2-MSP_DevTools.git"}, "xxamasty": {"type": "composer", "url": "https://composer.amasty.com/community/", "canonical": false}, "xxamasty2": {"type": "composer", "url": "https://1eb0c522f405d46d0780ecff182dabf2:<EMAIL>/community/", "canonical": false}, "perspective-magento2-partytown": {"type": "vcs", "url": "https://github.com/rostilos/perspective-partytown.git"}}, "extra": {"magento-force": "override", "composer-exit-on-patch-failure": true, "patches": {"dan0sz/resource-hints-magento2": {"crossorigin": "config/patches/dan0sz/resource-hints-magento2/crossorigin.patch"}, "vpietri/adm-quickdevbar": {"jquery-tabs": "config/patches/vpietri/adm-quickdevbar/jquery-tabs.patch"}, "magento/module-cms": {"prefix": "config/patches/magento/module-cms/no_validation_error.patch"}, "magento/module-security": {"adminsession": "config/patches/magento/module-security/adminsession.patch"}, "magento/framework": {"ProductLayoutApply": "config/patches/magento/framework/Magento_8333_pdp_layout.patch", "38214": "config/patches/magento/framework/38214.patch"}, "klarna/module-payments": {"no_such_entity_logging": "config/patches/klarna/module-payments/log_error.patch"}, "clever/magento2": {"blacklist_log": "config/patches/clever/magento2/blacklist_log.patch"}, "mageworx/module-seobase": {"layer": "config/patches/mageworx/module-seobase/layer.patch"}, "mageworx/module-seoextended": {"layer": "config/patches/mageworx/module-seoextended/layer.patch"}, "magefan/module-community": {"nohome": "config/patches/magefan/module-community/nohome.patch"}, "magefan/module-wysiwyg-advanced": {"nohome": "config/patches/magefan/module-wysiwyg-advanced/nocommunity.patch", "small": "config/patches/magefan/module-wysiwyg-advanced/small.patch"}, "magento/module-customer": {"checkout-error": "config/patches/magento/module-customer/notinitialized.patch", "magepack-error": "config/patches/magento/module-customer/magepack-fix-error.patch"}, "smile/elasticsuite": {"product-sorter": "config/patches/smile/elasticsuite/product-sorter.patch", "tracker_php81": "config/patches/smile/elasticsuite/tracker_php81.patch", "csp_tracker": "config/patches/smile/elasticsuite/csp_tracker.patch"}, "magento/module-reports": {"csv": "config/patches/magento/module-reports/csv.patch", "excel": "config/patches/magento/module-reports/excel.patch"}, "xtento/xtcore": {"csp": "config/patches/xtento/xtcore/csp.patch"}}, "magento-deploy-ignore": {"*": ["/pub/errors/default/503.phtml", "/pub/errors/default/page.phtml", "/pub/errors/default/css/styles.css", ".giti<PERSON>re"]}}, "config": {"allow-plugins": {"laminas/laminas-dependency-plugin": true, "magento/magento-composer-installer": true, "magento/inventory-composer-installer": true, "cweagans/composer-patches": true, "magento/composer-dependency-version-audit-plugin": false, "magento/composer-root-update-plugin": true, "dealerdirect/phpcodesniffer-composer-installer": true}}}